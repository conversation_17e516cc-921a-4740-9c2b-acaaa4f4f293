import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/call_log_entry.dart';
import '../providers/app_provider.dart';
import '../constants/app_constants.dart';

class CallLogScreen extends ConsumerStatefulWidget {
  const CallLogScreen({super.key});

  @override
  ConsumerState<CallLogScreen> createState() => _CallLogScreenState();
}

class _CallLogScreenState extends ConsumerState<CallLogScreen> {
  CallType? _selectedFilter;

  @override
  Widget build(BuildContext context) {
    final callLogsAsync = ref.watch(callLogProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Call Log'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              _showFilterDialog();
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              _showMoreOptions(context);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter Chips
          if (_selectedFilter != null)
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Row(
                children: [
                  Chip(
                    label: Text(_getFilterLabel(_selectedFilter!)),
                    onDeleted: () {
                      setState(() {
                        _selectedFilter = null;
                      });
                    },
                    backgroundColor: AppConstants.primaryColor.withValues(alpha: 0.1),
                    deleteIcon: const Icon(Icons.close, size: 18),
                  ),
                ],
              ),
            ),

          // Call Logs List
          Expanded(
            child: callLogsAsync.when(
              data: (callLogs) {
                final filteredLogs = _selectedFilter != null
                    ? callLogs.where((log) => log.callType == _selectedFilter).toList()
                    : callLogs;

                if (filteredLogs.isEmpty) {
                  return _buildEmptyState();
                }

                return ListView.builder(
                  itemCount: filteredLogs.length,
                  itemBuilder: (context, index) {
                    final callLog = filteredLogs[index];
                    return _buildCallLogTile(callLog);
                  },
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stack) => _buildErrorState(error.toString()),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCallLogTile(CallLogEntry callLog) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getCallTypeColor(callLog.callType),
          child: Icon(
            _getCallTypeIcon(callLog.callType),
            color: Colors.white,
          ),
        ),
        title: Text(
          callLog.contactName ?? callLog.phoneNumber,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: AppConstants.primaryTextColor,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              callLog.phoneNumber,
              style: const TextStyle(
                color: AppConstants.secondaryTextColor,
              ),
            ),
            Row(
              children: [
                Icon(
                  _getCallTypeIcon(callLog.callType),
                  size: 16,
                  color: _getCallTypeColor(callLog.callType),
                ),
                const SizedBox(width: 4),
                Text(
                  _getCallTypeLabel(callLog.callType),
                  style: TextStyle(
                    color: _getCallTypeColor(callLog.callType),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (callLog.duration != null) ...[
                  const SizedBox(width: 8),
                  Text(
                    callLog.formattedDuration,
                    style: const TextStyle(
                      color: AppConstants.secondaryTextColor,
                      fontSize: 12,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              callLog.formattedTime,
              style: const TextStyle(
                fontSize: 12,
                color: AppConstants.secondaryTextColor,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.call),
                  onPressed: () => _makeCall(callLog.phoneNumber),
                  color: AppConstants.primaryColor,
                  iconSize: 20,
                ),
                IconButton(
                  icon: const Icon(Icons.more_vert),
                  onPressed: () => _showCallLogOptions(callLog),
                  color: AppConstants.secondaryTextColor,
                  iconSize: 20,
                ),
              ],
            ),
          ],
        ),
        onTap: () => _makeCall(callLog.phoneNumber),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: AppConstants.secondaryTextColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'No call history',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppConstants.primaryTextColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Your call history will appear here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppConstants.errorColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'Error loading call logs',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppConstants.primaryTextColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          ElevatedButton(
            onPressed: () {
              ref.read(callLogProvider.notifier).loadCallLogs();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Color _getCallTypeColor(CallType callType) {
    switch (callType) {
      case CallType.incoming:
        return AppConstants.incomingCallColor;
      case CallType.outgoing:
        return AppConstants.outgoingCallColor;
      case CallType.missed:
        return AppConstants.missedCallColor;
      case CallType.rejected:
        return AppConstants.errorColor;
      case CallType.blocked:
        return AppConstants.blockedCallColor;
    }
  }

  IconData _getCallTypeIcon(CallType callType) {
    switch (callType) {
      case CallType.incoming:
        return Icons.call_received;
      case CallType.outgoing:
        return Icons.call_made;
      case CallType.missed:
        return Icons.call_missed;
      case CallType.rejected:
        return Icons.call_end;
      case CallType.blocked:
        return Icons.block;
    }
  }

  String _getCallTypeLabel(CallType callType) {
    switch (callType) {
      case CallType.incoming:
        return 'Incoming';
      case CallType.outgoing:
        return 'Outgoing';
      case CallType.missed:
        return 'Missed';
      case CallType.rejected:
        return 'Rejected';
      case CallType.blocked:
        return 'Blocked';
    }
  }

  String _getFilterLabel(CallType callType) {
    switch (callType) {
      case CallType.incoming:
        return 'Incoming calls only';
      case CallType.outgoing:
        return 'Outgoing calls only';
      case CallType.missed:
        return 'Missed calls only';
      case CallType.rejected:
        return 'Rejected calls only';
      case CallType.blocked:
        return 'Blocked calls only';
    }
  }

  Future<void> _makeCall(String phoneNumber) async {
    try {
      await ref.read(callLogProvider.notifier).makeCall(phoneNumber);
      ref.read(currentCallProvider.notifier).startCall(phoneNumber);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Calling $phoneNumber...'),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to make call: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Call Log'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: CallType.values.map((callType) {
            return ListTile(
              title: Text(_getCallTypeLabel(callType)),
              leading: Icon(
                _getCallTypeIcon(callType),
                color: _getCallTypeColor(callType),
              ),
              onTap: () {
                setState(() {
                  _selectedFilter = callType;
                });
                Navigator.pop(context);
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showCallLogOptions(CallLogEntry callLog) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.call),
              title: const Text('Call'),
              onTap: () {
                Navigator.pop(context);
                _makeCall(callLog.phoneNumber);
              },
            ),
            ListTile(
              leading: const Icon(Icons.block),
              title: const Text('Block Number'),
              onTap: () {
                Navigator.pop(context);
                _blockNumber(callLog.phoneNumber);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete),
              title: const Text('Delete'),
              onTap: () {
                Navigator.pop(context);
                _deleteCallLog(callLog);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.delete_sweep),
              title: const Text('Clear All Call Logs'),
              onTap: () {
                Navigator.pop(context);
                _clearAllCallLogs();
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Call Log Settings'),
              onTap: () {
                Navigator.pop(context);
                // Call log settings
              },
            ),
          ],
        ),
      ),
    );
  }

  void _blockNumber(String phoneNumber) {
    ref.read(contactProvider.notifier).blockNumber(phoneNumber);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$phoneNumber has been blocked'),
        backgroundColor: AppConstants.successColor,
      ),
    );
  }

  void _deleteCallLog(CallLogEntry callLog) {
    ref.read(callLogProvider.notifier).deleteCallLog(callLog.id);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Call log deleted'),
        backgroundColor: AppConstants.successColor,
      ),
    );
  }

  void _clearAllCallLogs() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Call Logs'),
        content: const Text('Are you sure you want to delete all call logs? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(callLogProvider.notifier).deleteAllCallLogs();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('All call logs cleared'),
                  backgroundColor: AppConstants.successColor,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
            ),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }
} 