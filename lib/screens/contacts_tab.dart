import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_theme.dart';
import '../models/contact_model.dart';
import '../providers/app_providers.dart';
import '../widgets/contact_list_item.dart';
import '../widgets/banner_widget.dart';
import '../widgets/empty_state_widget.dart';

/// Contacts tab showing all contacts
/// Includes banner and alphabetically sorted contact list
class ContactsTab extends ConsumerWidget {
  const ContactsTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final contactsAsync = ref.watch(contactsProvider);
    final searchQuery = ref.watch(searchProvider);
    final bannerVisibility = ref.watch(bannerVisibilityProvider);

    return Column(
      children: [
        // Spam Protection Banner
        if (bannerVisibility['spam_protection'] == true)
          BannerWidget(
            id: 'spam_protection',
            icon: Icons.block,
            title: 'Spam Protection',
            description: 'Block spam calls automatically and keep your phone safe from unwanted calls.',
            actionText: 'Enable Protection',
            onAction: () {
              _showSpamProtectionDialog(context);
            },
            onClose: () {
              ref.read(bannerVisibilityProvider.notifier).hideBanner('spam_protection');
            },
            backgroundColor: AppTheme.errorColor.withValues(alpha: 0.1),
            iconColor: AppTheme.errorColor,
          ),

        // Contacts list
        Expanded(
          child: contactsAsync.when(
            data: (contacts) {
              if (contacts.isEmpty) {
                return const EmptyStateWidget(
                  icon: Icons.contacts,
                  title: 'No Contacts',
                  description: 'Add contacts to see them here. You can import from your device or add manually.',
                  actionText: 'Add Contact',
                );
              }

              // Filter contacts based on search query
              final filteredContacts = searchQuery.isEmpty
                  ? contacts
                  : contacts.where((contact) {
                      final query = searchQuery.toLowerCase();
                      return contact.name.toLowerCase().contains(query) ||
                          contact.phoneNumbers.any((phone) => phone.number.contains(searchQuery)) ||
                          (contact.company?.toLowerCase().contains(query) ?? false);
                    }).toList();

              if (filteredContacts.isEmpty && searchQuery.isNotEmpty) {
                return EmptyStateWidget(
                  icon: Icons.search_off,
                  title: 'No Results Found',
                  description: 'No contacts match "$searchQuery". Try a different search term.',
                );
              }

              // Group contacts by first letter
              final groupedContacts = _groupContactsByLetter(filteredContacts);

              return ListView.builder(
                padding: const EdgeInsets.symmetric(vertical: AppTheme.spacing8),
                itemCount: groupedContacts.length,
                itemBuilder: (context, index) {
                  final group = groupedContacts[index];
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Section header
                      if (searchQuery.isEmpty) _buildSectionHeader(group.letter),
                      
                      // Contacts in this group
                      ...group.contacts.map((contact) => ContactListItem(
                        contact: contact,
                        onTap: () => _showContactDetails(context, contact.id),
                        onCall: () => _makeCall(context, ref, contact.primaryPhoneNumber),
                        onMessage: () => _sendMessage(context, contact.primaryPhoneNumber),
                        showFavoriteButton: true,
                        showMessageButton: true,
                      )),
                    ],
                  );
                },
              );
            },
            loading: () => const Center(
              child: CircularProgressIndicator(
                color: AppTheme.primaryGreen,
              ),
            ),
            error: (error, stack) => EmptyStateWidget(
              icon: Icons.error_outline,
              title: 'Error Loading Contacts',
              description: 'Failed to load your contacts. Please try again.',
              actionText: 'Retry',
              onAction: () {
                ref.read(contactsProvider.notifier).loadContacts();
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String letter) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing24,
        vertical: AppTheme.spacing8,
      ),
      child: Text(
        letter,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: AppTheme.primaryGreen,
        ),
      ),
    );
  }

  List<ContactGroup> _groupContactsByLetter(List<ContactModel> contacts) {
    final Map<String, List<ContactModel>> grouped = {};
    
    for (final contact in contacts) {
      final firstLetter = contact.name.isNotEmpty 
          ? contact.name[0].toUpperCase() 
          : '#';
      
      if (!grouped.containsKey(firstLetter)) {
        grouped[firstLetter] = [];
      }
      grouped[firstLetter]!.add(contact);
    }
    
    // Sort groups by letter and contacts within each group
    final sortedKeys = grouped.keys.toList()..sort();
    return sortedKeys.map((letter) {
      final groupContacts = grouped[letter]!;
      groupContacts.sort((a, b) => a.name.compareTo(b.name));
      return ContactGroup(letter: letter, contacts: groupContacts);
    }).toList();
  }

  void _showSpamProtectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
        ),
        title: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppTheme.errorColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              ),
              child: const Icon(
                Icons.block,
                color: AppTheme.errorColor,
                size: 20,
              ),
            ),
            const SizedBox(width: AppTheme.spacing12),
            const Text('Spam Protection'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Enable spam protection to get enhanced security:',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: AppTheme.spacing16),
            _FeatureItem(
              icon: Icons.block,
              text: 'Automatic spam call blocking',
            ),
            _FeatureItem(
              icon: Icons.warning,
              text: 'Real-time spam detection',
            ),
            _FeatureItem(
              icon: Icons.report,
              text: 'Community-based spam reporting',
            ),
            _FeatureItem(
              icon: Icons.security,
              text: 'Fraud call protection',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Not Now'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement spam protection
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Spam protection enabled!'),
                  backgroundColor: AppTheme.successColor,
                ),
              );
            },
            child: const Text('Enable'),
          ),
        ],
      ),
    );
  }

  void _showContactDetails(BuildContext context, String contactId) {
    // TODO: Navigate to contact details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Contact details for $contactId'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }

  void _makeCall(BuildContext context, WidgetRef ref, String? phoneNumber) {
    if (phoneNumber == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No phone number available'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    // TODO: Implement actual call functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Calling $phoneNumber...'),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  void _sendMessage(BuildContext context, String? phoneNumber) {
    if (phoneNumber == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No phone number available'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    // TODO: Implement messaging functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sending message to $phoneNumber...'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }
}

class ContactGroup {
  final String letter;
  final List<ContactModel> contacts;

  ContactGroup({
    required this.letter,
    required this.contacts,
  });
}

class _FeatureItem extends StatelessWidget {
  final IconData icon;
  final String text;

  const _FeatureItem({
    required this.icon,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppTheme.spacing4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: AppTheme.errorColor,
          ),
          const SizedBox(width: AppTheme.spacing8),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }
}


