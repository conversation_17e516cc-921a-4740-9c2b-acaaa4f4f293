import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_theme.dart';
import '../providers/app_providers.dart';

/// Settings screen for Easy Dialer
/// Comprehensive settings with all options and recommended apps section
class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
        title: const Text('Settings'),
        elevation: 0,
      ),
      body: ListView(
        padding: const EdgeInsets.symmetric(vertical: AppTheme.spacing16),
        children: [
          // General Settings
          _buildSectionHeader('General'),
          _buildSettingsTile(
            icon: Icons.phone,
            title: 'Default Dialer',
            subtitle: 'Set Easy Dialer as default phone app',
            onTap: () => _showDefaultDialerDialog(context),
          ),
          _buildSettingsTile(
            icon: Icons.notifications,
            title: 'Notifications',
            subtitle: 'Manage call and message notifications',
            onTap: () => _showNotificationSettings(context),
          ),
          _buildSettingsTile(
            icon: Icons.volume_up,
            title: 'Sounds & Vibration',
            subtitle: 'Ringtones, notification sounds, and vibration',
            onTap: () => _showSoundSettings(context),
          ),

          const SizedBox(height: AppTheme.spacing24),

          // Privacy & Security
          _buildSectionHeader('Privacy & Security'),
          _buildSettingsTile(
            icon: Icons.block,
            title: 'Blocked Numbers',
            subtitle: 'Manage blocked contacts and spam protection',
            onTap: () => _showBlockedNumbers(context),
          ),
          _buildSettingsTile(
            icon: Icons.security,
            title: 'Caller ID & Spam',
            subtitle: 'Enable caller identification and spam filtering',
            onTap: () => _showCallerIdSettings(context),
          ),
          _buildSettingsTile(
            icon: Icons.privacy_tip,
            title: 'Privacy',
            subtitle: 'Control data sharing and privacy settings',
            onTap: () => _showPrivacySettings(context),
          ),

          const SizedBox(height: AppTheme.spacing24),

          // Contacts & Backup
          _buildSectionHeader('Contacts & Backup'),
          _buildSettingsTile(
            icon: Icons.sync,
            title: 'Sync Contacts',
            subtitle: 'Sync contacts with Google, iCloud, or other accounts',
            onTap: () => _showSyncSettings(context),
          ),
          _buildSettingsTile(
            icon: Icons.backup,
            title: 'Backup & Restore',
            subtitle: 'Backup contacts and call history',
            onTap: () => _showBackupSettings(context),
          ),
          _buildSettingsTile(
            icon: Icons.import_export,
            title: 'Import/Export',
            subtitle: 'Import or export contacts from files',
            onTap: () => _showImportExportSettings(context),
          ),

          const SizedBox(height: AppTheme.spacing24),

          // Appearance
          _buildSectionHeader('Appearance'),
          _buildSettingsTile(
            icon: Icons.palette,
            title: 'Theme',
            subtitle: 'Choose light, dark, or system theme',
            onTap: () => _showThemeSettings(context),
          ),
          _buildSettingsTile(
            icon: Icons.text_fields,
            title: 'Font Size',
            subtitle: 'Adjust text size for better readability',
            onTap: () => _showFontSettings(context),
          ),
          _buildSettingsTile(
            icon: Icons.language,
            title: 'Language',
            subtitle: 'Change app language',
            onTap: () => _showLanguageSettings(context),
          ),

          const SizedBox(height: AppTheme.spacing24),

          // Advanced
          _buildSectionHeader('Advanced'),
          _buildSettingsTile(
            icon: Icons.storage,
            title: 'Storage',
            subtitle: 'Manage app data and cache',
            onTap: () => _showStorageSettings(context),
          ),
          _buildSettingsTile(
            icon: Icons.bug_report,
            title: 'Debug',
            subtitle: 'Debug options and logs',
            onTap: () => _showDebugSettings(context),
          ),
          _buildSettingsTile(
            icon: Icons.system_update,
            title: 'Updates',
            subtitle: 'Check for app updates',
            onTap: () => _checkForUpdates(context),
          ),

          const SizedBox(height: AppTheme.spacing24),

          // About
          _buildSectionHeader('About'),
          _buildSettingsTile(
            icon: Icons.help,
            title: 'Help & Support',
            subtitle: 'Get help and contact support',
            onTap: () => _showHelpSupport(context),
          ),
          _buildSettingsTile(
            icon: Icons.star_rate,
            title: 'Rate App',
            subtitle: 'Rate Easy Dialer on the app store',
            onTap: () => _rateApp(context),
          ),
          _buildSettingsTile(
            icon: Icons.share,
            title: 'Share App',
            subtitle: 'Share Easy Dialer with friends',
            onTap: () => _shareApp(context),
          ),
          _buildSettingsTile(
            icon: Icons.info,
            title: 'About',
            subtitle: 'App version and legal information',
            onTap: () => _showAbout(context),
          ),

          const SizedBox(height: AppTheme.spacing32),

          // More Recommended Apps
          _buildRecommendedAppsSection(),

          const SizedBox(height: AppTheme.spacing24),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing24,
        vertical: AppTheme.spacing8,
      ),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: AppTheme.primaryGreen,
        ),
      ),
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Widget? trailing,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing16,
        vertical: AppTheme.spacing4,
      ),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
      ),
      child: ListTile(
        onTap: onTap,
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppTheme.primaryGreen.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          ),
          child: Icon(
            icon,
            color: AppTheme.primaryGreen,
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: AppTheme.textPrimary,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: const TextStyle(
            fontSize: 14,
            color: AppTheme.textSecondary,
          ),
        ),
        trailing: trailing ?? const Icon(
          Icons.chevron_right,
          color: AppTheme.textSecondary,
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppTheme.spacing16,
          vertical: AppTheme.spacing8,
        ),
      ),
    );
  }

  Widget _buildRecommendedAppsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('More Recommended Apps'),
        
        Card(
          margin: const EdgeInsets.symmetric(horizontal: AppTheme.spacing16),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.spacing16),
            child: Column(
              children: [
                _buildRecommendedApp(
                  icon: Icons.message,
                  name: 'Easy Messages',
                  description: 'Beautiful messaging app with smart features',
                  rating: 4.8,
                ),
                const Divider(height: AppTheme.spacing24),
                _buildRecommendedApp(
                  icon: Icons.contacts,
                  name: 'Smart Contacts',
                  description: 'Advanced contact management and backup',
                  rating: 4.7,
                ),
                const Divider(height: AppTheme.spacing24),
                _buildRecommendedApp(
                  icon: Icons.security,
                  name: 'Call Guard',
                  description: 'Advanced spam protection and call blocking',
                  rating: 4.9,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRecommendedApp({
    required IconData icon,
    required String name,
    required String description,
    required double rating,
  }) {
    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: AppTheme.primaryGreen,
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          ),
          child: Icon(
            icon,
            color: Colors.white,
            size: 24,
          ),
        ),
        
        const SizedBox(width: AppTheme.spacing16),
        
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondary,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(
                    Icons.star,
                    size: 16,
                    color: AppTheme.warningColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    rating.toString(),
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        
        ElevatedButton(
          onPressed: () {
            // TODO: Open app store
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryGreen,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacing16,
              vertical: AppTheme.spacing8,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            ),
          ),
          child: const Text(
            'Install',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  // Settings dialog methods
  void _showDefaultDialerDialog(BuildContext context) {
    _showInfoDialog(context, 'Default Dialer', 'Set Easy Dialer as your default phone app to handle all calls.');
  }

  void _showNotificationSettings(BuildContext context) {
    _showInfoDialog(context, 'Notifications', 'Manage call notifications, missed call alerts, and message notifications.');
  }

  void _showSoundSettings(BuildContext context) {
    _showInfoDialog(context, 'Sounds & Vibration', 'Customize ringtones, notification sounds, and vibration patterns.');
  }

  void _showBlockedNumbers(BuildContext context) {
    _showInfoDialog(context, 'Blocked Numbers', 'View and manage your blocked contacts and phone numbers.');
  }

  void _showCallerIdSettings(BuildContext context) {
    _showInfoDialog(context, 'Caller ID & Spam', 'Enable caller identification and automatic spam filtering.');
  }

  void _showPrivacySettings(BuildContext context) {
    _showInfoDialog(context, 'Privacy', 'Control what data is shared and manage your privacy preferences.');
  }

  void _showSyncSettings(BuildContext context) {
    _showInfoDialog(context, 'Sync Contacts', 'Sync your contacts with Google, iCloud, or other cloud services.');
  }

  void _showBackupSettings(BuildContext context) {
    _showInfoDialog(context, 'Backup & Restore', 'Backup your contacts and call history to the cloud.');
  }

  void _showImportExportSettings(BuildContext context) {
    _showInfoDialog(context, 'Import/Export', 'Import contacts from files or export your contacts.');
  }

  void _showThemeSettings(BuildContext context) {
    _showInfoDialog(context, 'Theme', 'Choose between light theme, dark theme, or follow system settings.');
  }

  void _showFontSettings(BuildContext context) {
    _showInfoDialog(context, 'Font Size', 'Adjust the text size throughout the app for better readability.');
  }

  void _showLanguageSettings(BuildContext context) {
    _showInfoDialog(context, 'Language', 'Change the app language to your preferred language.');
  }

  void _showStorageSettings(BuildContext context) {
    _showInfoDialog(context, 'Storage', 'View app storage usage and clear cache if needed.');
  }

  void _showDebugSettings(BuildContext context) {
    _showInfoDialog(context, 'Debug', 'Advanced debugging options and error logs for troubleshooting.');
  }

  void _checkForUpdates(BuildContext context) {
    _showInfoDialog(context, 'Updates', 'Check for the latest version of Easy Dialer.');
  }

  void _showHelpSupport(BuildContext context) {
    _showInfoDialog(context, 'Help & Support', 'Get help with using the app or contact our support team.');
  }

  void _rateApp(BuildContext context) {
    _showInfoDialog(context, 'Rate App', 'Rate Easy Dialer on the app store to help us improve.');
  }

  void _shareApp(BuildContext context) {
    _showInfoDialog(context, 'Share App', 'Share Easy Dialer with your friends and family.');
  }

  void _showAbout(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'Easy Dialer',
      applicationVersion: '1.0.0',
      applicationIcon: Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: AppTheme.primaryGreen,
          borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        ),
        child: const Icon(
          Icons.phone,
          color: Colors.white,
          size: 32,
        ),
      ),
      children: const [
        Text('A beautiful and feature-rich dialer app with caller ID, spam protection, and modern design.'),
        SizedBox(height: 16),
        Text('Built with Flutter and Material Design 3.'),
      ],
    );
  }

  void _showInfoDialog(BuildContext context, String title, String content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
