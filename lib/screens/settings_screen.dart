import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/app_provider.dart';
import '../constants/app_constants.dart';

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        children: [
          // Theme Section
          _buildSectionHeader('Appearance'),
          _buildThemeSelector(context, ref, themeMode),
          
          const Divider(),
          
          // Call Settings
          _buildSectionHeader('Call Settings'),
          _buildListTile(
            icon: Icons.block,
            title: 'Blocked Numbers',
            subtitle: 'Manage blocked phone numbers',
            onTap: () {
              _showBlockedNumbers(context, ref);
            },
          ),
          _buildListTile(
            icon: Icons.notifications,
            title: 'Call Notifications',
            subtitle: 'Configure call notifications',
            onTap: () {
              // Call notifications settings
            },
          ),
          
          const Divider(),
          
          // Privacy & Security
          _buildSectionHeader('Privacy & Security'),
          _buildListTile(
            icon: Icons.security,
            title: 'Caller ID',
            subtitle: 'Enable caller identification',
            onTap: () {
              // Caller ID settings
            },
          ),
          _buildListTile(
            icon: Icons.shield,
            title: 'Spam Protection',
            subtitle: 'Block spam calls automatically',
            onTap: () {
              // Spam protection settings
            },
          ),
          
          const Divider(),
          
          // Data & Storage
          _buildSectionHeader('Data & Storage'),
          _buildListTile(
            icon: Icons.delete_sweep,
            title: 'Clear Call Logs',
            subtitle: 'Delete all call history',
            onTap: () {
              _showClearCallLogsDialog(context, ref);
            },
          ),
          _buildListTile(
            icon: Icons.backup,
            title: 'Backup & Restore',
            subtitle: 'Backup your contacts and settings',
            onTap: () {
              // Backup and restore
            },
          ),
          
          const Divider(),
          
          // About
          _buildSectionHeader('About'),
          _buildListTile(
            icon: Icons.info,
            title: 'App Version',
            subtitle: AppConstants.appVersion,
            onTap: null,
          ),
          _buildListTile(
            icon: Icons.description,
            title: 'Privacy Policy',
            subtitle: 'Read our privacy policy',
            onTap: () {
              // Open privacy policy
            },
          ),
          _buildListTile(
            icon: Icons.description,
            title: 'Terms of Service',
            subtitle: 'Read our terms of service',
            onTap: () {
              // Open terms of service
            },
          ),
          _buildListTile(
            icon: Icons.help,
            title: 'Help & Support',
            subtitle: 'Get help and contact support',
            onTap: () {
              // Help and support
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(
        AppConstants.paddingLarge,
        AppConstants.paddingLarge,
        AppConstants.paddingLarge,
        AppConstants.paddingSmall,
      ),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: AppConstants.primaryColor,
        ),
      ),
    );
  }

  Widget _buildListTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback? onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppConstants.primaryColor,
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w500,
          color: AppConstants.primaryTextColor,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          color: AppConstants.secondaryTextColor,
        ),
      ),
      trailing: onTap != null
          ? const Icon(
              Icons.chevron_right,
              color: AppConstants.secondaryTextColor,
            )
          : null,
      onTap: onTap,
    );
  }

  Widget _buildThemeSelector(BuildContext context, WidgetRef ref, ThemeMode themeMode) {
    return Column(
      children: [
        _buildListTile(
          icon: Icons.light_mode,
          title: 'Light Theme',
          subtitle: 'Use light theme',
          onTap: () {
            ref.read(themeProvider.notifier).setTheme(ThemeMode.light);
          },
        ),
        _buildListTile(
          icon: Icons.dark_mode,
          title: 'Dark Theme',
          subtitle: 'Use dark theme',
          onTap: () {
            ref.read(themeProvider.notifier).setTheme(ThemeMode.dark);
          },
        ),
        _buildListTile(
          icon: Icons.settings_system_daydream,
          title: 'System Theme',
          subtitle: 'Follow system theme',
          onTap: () {
            ref.read(themeProvider.notifier).setTheme(ThemeMode.system);
          },
        ),
        // Theme indicator
        Container(
          margin: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingLarge,
            vertical: AppConstants.paddingSmall,
          ),
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          decoration: BoxDecoration(
            color: AppConstants.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          ),
          child: Row(
            children: [
              Icon(
                _getThemeIcon(themeMode),
                color: AppConstants.primaryColor,
                size: 20,
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              Text(
                'Current: ${_getThemeLabel(themeMode)}',
                style: const TextStyle(
                  color: AppConstants.primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  IconData _getThemeIcon(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.settings_system_daydream;
    }
  }

  String _getThemeLabel(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }

  void _showBlockedNumbers(BuildContext context, WidgetRef ref) {
    final blockedNumbers = ref.read(contactProvider.notifier).blockedNumbers;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Blocked Numbers'),
        content: SizedBox(
          width: double.maxFinite,
          child: blockedNumbers.isEmpty
              ? const Center(
                  child: Text('No blocked numbers'),
                )
              : ListView.builder(
                  shrinkWrap: true,
                  itemCount: blockedNumbers.length,
                  itemBuilder: (context, index) {
                    final number = blockedNumbers[index];
                    return ListTile(
                      title: Text(number),
                      trailing: IconButton(
                        icon: const Icon(Icons.remove_circle_outline),
                        onPressed: () {
                          ref.read(contactProvider.notifier).unblockNumber(number);
                          Navigator.pop(context);
                          _showBlockedNumbers(context, ref);
                        },
                      ),
                    );
                  },
                ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showClearCallLogsDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Call Logs'),
        content: const Text(
          'Are you sure you want to delete all call logs? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(callLogProvider.notifier).deleteAllCallLogs();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('All call logs cleared'),
                  backgroundColor: AppConstants.successColor,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
            ),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }
} 