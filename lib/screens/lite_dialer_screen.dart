import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/app_provider.dart';
import '../models/contact.dart';
import '../models/call_log_entry.dart';

class LiteDialerScreen extends ConsumerStatefulWidget {
  const LiteDialerScreen({super.key});

  @override
  ConsumerState<LiteDialerScreen> createState() => _LiteDialerScreenState();
}

class _LiteDialerScreenState extends ConsumerState<LiteDialerScreen> {
  String _phoneNumber = '';
  bool _showDialPad = true;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    // Load contacts and call logs when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(contactProvider.notifier).loadContacts();
      ref.read(callLogProvider.notifier).loadCallLogs();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Header with search and menu
            _buildHeader(),

            // Phone number display
            _buildPhoneNumberDisplay(),

            // Main content area
            Expanded(
              child: _showDialPad ? _buildDialPad() : _buildRecentCalls(),
            ),

            // Bottom navigation
            _buildBottomNavigation(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Search icon
          IconButton(
            onPressed: () {
              setState(() {
                _showDialPad = false;
              });
            },
            icon: const Icon(Icons.search, size: 24),
            color: Colors.grey[600],
          ),

          const Spacer(),

          // App title
          Text(
            'Simpler Dialer',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey[800],
            ),
          ),

          const Spacer(),

          // Menu icon
          IconButton(
            onPressed: () {
              // Show menu options
            },
            icon: const Icon(Icons.more_vert, size: 24),
            color: Colors.grey[600],
          ),
        ],
      ),
    );
  }

  Widget _buildPhoneNumberDisplay() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Column(
        children: [
          // Phone number text
          Container(
            height: 60,
            alignment: Alignment.center,
            child: Text(
              _phoneNumber.isEmpty ? '' : _phoneNumber,
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.w300,
                color: Colors.black87,
                letterSpacing: 2,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // Action buttons row
          if (_phoneNumber.isNotEmpty) ...[
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Backspace button
                IconButton(
                  onPressed: () {
                    setState(() {
                      if (_phoneNumber.isNotEmpty) {
                        _phoneNumber = _phoneNumber.substring(0, _phoneNumber.length - 1);
                      }
                    });
                  },
                  icon: const Icon(Icons.backspace_outlined),
                  iconSize: 24,
                  color: Colors.grey[600],
                ),

                const SizedBox(width: 32),

                // Call button
                Container(
                  decoration: BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    onPressed: () => _makeCall(_phoneNumber),
                    icon: const Icon(Icons.call),
                    iconSize: 28,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDialPad() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 1.0,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: 12,
        itemBuilder: (context, index) {
          if (index == 9) {
            return _buildDialButton('*', '');
          } else if (index == 10) {
            return _buildDialButton('0', '+');
          } else if (index == 11) {
            return _buildDialButton('#', '');
          } else {
            return _buildDialButton('${index + 1}', _getLetters(index + 1));
          }
        },
      ),
    );
  }

  Widget _buildDialButton(String number, String letters) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          setState(() {
            _phoneNumber += number;
          });
        },
        borderRadius: BorderRadius.circular(50),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.grey[50],
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.grey[200]!,
              width: 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                number,
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.w400,
                  color: Colors.black87,
                ),
              ),
              if (letters.isNotEmpty) ...[
                const SizedBox(height: 2),
                Text(
                  letters,
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w400,
                    color: Colors.grey[600],
                    letterSpacing: 1.5,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentCalls() {
    return Consumer(
      builder: (context, ref, child) {
        final callLogsAsync = ref.watch(callLogProvider);
        return callLogsAsync.when(
          data: (callLogs) {
            if (callLogs.isEmpty) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.history, size: 64, color: Colors.grey),
                    SizedBox(height: 16),
                    Text(
                      'No recent calls',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              );
            }

            return ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: callLogs.length,
              itemBuilder: (context, index) {
                final callLog = callLogs[index];
                return _buildRecentCallTile(callLog);
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => const Center(
            child: Text('Error loading call logs'),
          ),
        );
      },
    );
  }

  Widget _buildRecentCallTile(CallLogEntry callLog) {
    IconData callIcon;
    Color callColor;

    switch (callLog.callType) {
      case CallType.incoming:
        callIcon = Icons.call_received;
        callColor = Colors.green;
        break;
      case CallType.outgoing:
        callIcon = Icons.call_made;
        callColor = Colors.blue;
        break;
      case CallType.missed:
        callIcon = Icons.call_received;
        callColor = Colors.red;
        break;
      default:
        callIcon = Icons.call;
        callColor = Colors.grey;
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Icon(callIcon, color: callColor, size: 20),
        title: Text(
          callLog.contactName ?? callLog.phoneNumber,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (callLog.contactName != null)
              Text(
                callLog.phoneNumber,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            Text(
              _formatDateTime(callLog.timestamp),
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
        trailing: IconButton(
          icon: const Icon(Icons.call, color: Colors.green),
          onPressed: () => _makeCall(callLog.phoneNumber),
        ),
        onTap: () {
          setState(() {
            _phoneNumber = callLog.phoneNumber;
            _showDialPad = true;
          });
        },
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey[200]!, width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Recent calls button
          _buildNavButton(
            icon: Icons.history,
            label: 'Recent',
            isActive: !_showDialPad,
            onTap: () {
              setState(() {
                _showDialPad = false;
              });
            },
          ),

          // Dial pad button
          _buildNavButton(
            icon: Icons.dialpad,
            label: 'Keypad',
            isActive: _showDialPad,
            onTap: () {
              setState(() {
                _showDialPad = true;
              });
            },
          ),

          // Contacts button
          _buildNavButton(
            icon: Icons.contacts,
            label: 'Contacts',
            isActive: false,
            onTap: () {
              // Show contacts in a modal or navigate
              _showContactsModal();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildNavButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 24,
            color: isActive ? Colors.blue : Colors.grey[600],
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: isActive ? Colors.blue : Colors.grey[600],
              fontWeight: isActive ? FontWeight.w500 : FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  void _showContactsModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        expand: false,
        builder: (context, scrollController) => Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  const Text(
                    'Contacts',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // Search Bar
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: TextField(
                decoration: InputDecoration(
                  hintText: 'Search contacts...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  filled: true,
                  fillColor: Colors.grey[50],
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value.toLowerCase();
                  });
                },
              ),
            ),

            const SizedBox(height: 16),

            // Contacts List
            Expanded(
              child: Consumer(
                builder: (context, ref, child) {
                  final contactsAsync = ref.watch(contactProvider);
                  return contactsAsync.when(
                    data: (contacts) {
                      final filteredContacts = contacts.where((contact) {
                        return contact.name.toLowerCase().contains(_searchQuery) ||
                               contact.phoneNumbers.any((phone) => phone.contains(_searchQuery));
                      }).toList();

                      if (filteredContacts.isEmpty) {
                        return const Center(
                          child: Text('No contacts found'),
                        );
                      }

                      return ListView.builder(
                        controller: scrollController,
                        itemCount: filteredContacts.length,
                        itemBuilder: (context, index) {
                          final contact = filteredContacts[index];
                          return _buildContactTile(contact);
                        },
                      );
                    },
                    loading: () => const Center(child: CircularProgressIndicator()),
                    error: (error, stack) => const Center(
                      child: Text('Error loading contacts'),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactTile(Contact contact) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      leading: CircleAvatar(
        backgroundColor: Colors.blue,
        radius: 20,
        child: Text(
          contact.name.isNotEmpty ? contact.name[0].toUpperCase() : '?',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w500,
            fontSize: 16,
          ),
        ),
      ),
      title: Text(
        contact.name,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
      ),
      subtitle: Text(
        contact.phoneNumbers.isNotEmpty ? contact.phoneNumbers.first : 'No phone number',
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey[600],
        ),
      ),
      trailing: IconButton(
        icon: const Icon(Icons.call, color: Colors.green),
        onPressed: () {
          if (contact.phoneNumbers.isNotEmpty) {
            Navigator.pop(context); // Close modal
            _makeCall(contact.phoneNumbers.first);
          }
        },
      ),
      onTap: () {
        if (contact.phoneNumbers.isNotEmpty) {
          Navigator.pop(context); // Close modal
          setState(() {
            _phoneNumber = contact.phoneNumbers.first;
            _showDialPad = true;
          });
        }
      },
    );
  }



  String _getLetters(int number) {
    switch (number) {
      case 2: return 'ABC';
      case 3: return 'DEF';
      case 4: return 'GHI';
      case 5: return 'JKL';
      case 6: return 'MNO';
      case 7: return 'PQRS';
      case 8: return 'TUV';
      case 9: return 'WXYZ';
      default: return '';
    }
  }



  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  Future<void> _makeCall(String phoneNumber) async {
    try {
      final callService = ref.read(callServiceProvider);
      await callService.makeCall(phoneNumber);

      // Refresh call logs after making a call
      ref.read(callLogProvider.notifier).loadCallLogs();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Calling $phoneNumber...'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not make call: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
