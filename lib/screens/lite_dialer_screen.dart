import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import '../constants/app_constants.dart';
import '../providers/app_provider.dart';
import '../models/contact.dart';
import '../models/call_log_entry.dart';

class LiteDialerScreen extends ConsumerStatefulWidget {
  const LiteDialerScreen({super.key});

  @override
  ConsumerState<LiteDialerScreen> createState() => _LiteDialerScreenState();
}

class _LiteDialerScreenState extends ConsumerState<LiteDialerScreen>
    with SingleTickerProviderStateMixin {
  String _phoneNumber = '';
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    // Load contacts and call logs when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(contactProvider.notifier).loadContacts();
      ref.read(callLogProvider.notifier).loadCallLogs();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text('Lite Dialer'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.dialpad), text: 'Dialer'),
            Tab(icon: Icon(Icons.contacts), text: 'Contacts'),
            Tab(icon: Icon(Icons.history), text: 'Logs'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildDialerTab(),
          _buildContactsTab(),
          _buildCallLogsTab(),
        ],
      ),
    );
  }

  Widget _buildDialerTab() {
    return Column(
      children: [
        // Phone Number Display
        Container(
          width: double.infinity,
          margin: const EdgeInsets.all(AppConstants.paddingMedium),
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              Text(
                _phoneNumber.isEmpty ? 'Enter phone number' : _formatPhoneNumber(_phoneNumber),
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w400,
                  color: AppConstants.primaryTextColor,
                ),
                textAlign: TextAlign.center,
              ),
              if (_phoneNumber.isNotEmpty) ...[
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      onPressed: () {
                        setState(() {
                          if (_phoneNumber.isNotEmpty) {
                            _phoneNumber = _phoneNumber.substring(0, _phoneNumber.length - 1);
                          }
                        });
                      },
                      icon: const Icon(Icons.backspace_outlined),
                      color: AppConstants.errorColor,
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton.icon(
                      onPressed: () => _makeCall(_phoneNumber),
                      icon: const Icon(Icons.call),
                      label: const Text('Call'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppConstants.accentColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),

        // Dial Pad
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                childAspectRatio: 1.1,
                crossAxisSpacing: AppConstants.paddingMedium,
                mainAxisSpacing: AppConstants.paddingMedium,
              ),
              itemCount: 12,
              itemBuilder: (context, index) {
                if (index == 9) {
                  return _buildDialButton('*', '');
                } else if (index == 10) {
                  return _buildDialButton('0', '+');
                } else if (index == 11) {
                  return _buildDialButton('#', '');
                } else {
                  return _buildDialButton('${index + 1}', _getLetters(index + 1));
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDialButton(String number, String letters) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          setState(() {
            _phoneNumber += number;
          });
        },
        borderRadius: BorderRadius.circular(50),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                number,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w500,
                  color: AppConstants.primaryTextColor,
                ),
              ),
              if (letters.isNotEmpty)
                Text(
                  letters,
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w400,
                    color: AppConstants.secondaryTextColor,
                    letterSpacing: 1.0,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContactsTab() {
    return Column(
      children: [
        // Search Bar
        Container(
          margin: const EdgeInsets.all(AppConstants.paddingMedium),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search contacts...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value.toLowerCase();
              });
            },
          ),
        ),
        // Contacts List
        Expanded(
          child: Consumer(
            builder: (context, ref, child) {
              final contactsAsync = ref.watch(contactProvider);
              return contactsAsync.when(
                data: (contacts) {
                  final filteredContacts = contacts.where((contact) {
                    return contact.name.toLowerCase().contains(_searchQuery) ||
                           contact.phoneNumbers.any((phone) => phone.contains(_searchQuery));
                  }).toList();

                  if (filteredContacts.isEmpty) {
                    return const Center(
                      child: Text('No contacts found'),
                    );
                  }

                  return ListView.builder(
                    itemCount: filteredContacts.length,
                    itemBuilder: (context, index) {
                      final contact = filteredContacts[index];
                      return _buildContactTile(contact);
                    },
                  );
                },
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => Center(
                  child: Text('Error: $error'),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildContactTile(Contact contact) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: 4,
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppConstants.primaryColor,
          child: Text(
            contact.name.isNotEmpty ? contact.name[0].toUpperCase() : '?',
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        title: Text(contact.name),
        subtitle: Text(
          contact.phoneNumbers.isNotEmpty ? contact.phoneNumbers.first : 'No phone number',
        ),
        trailing: IconButton(
          icon: const Icon(Icons.call, color: AppConstants.accentColor),
          onPressed: () {
            if (contact.phoneNumbers.isNotEmpty) {
              _makeCall(contact.phoneNumbers.first);
            }
          },
        ),
        onTap: () {
          if (contact.phoneNumbers.isNotEmpty) {
            setState(() {
              _phoneNumber = contact.phoneNumbers.first;
              _tabController.animateTo(0); // Switch to dialer tab
            });
          }
        },
      ),
    );
  }

  Widget _buildCallLogsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final callLogsAsync = ref.watch(callLogProvider);
        return callLogsAsync.when(
          data: (callLogs) {
            if (callLogs.isEmpty) {
              return const Center(
                child: Text('No call logs found'),
              );
            }

            return ListView.builder(
              itemCount: callLogs.length,
              itemBuilder: (context, index) {
                final callLog = callLogs[index];
                return _buildCallLogTile(callLog);
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Text('Error: $error'),
          ),
        );
      },
    );
  }

  Widget _buildCallLogTile(CallLogEntry callLog) {
    IconData callIcon;
    Color callColor;

    switch (callLog.callType) {
      case CallType.incoming:
        callIcon = Icons.call_received;
        callColor = AppConstants.accentColor;
        break;
      case CallType.outgoing:
        callIcon = Icons.call_made;
        callColor = AppConstants.primaryColor;
        break;
      case CallType.missed:
        callIcon = Icons.call_received;
        callColor = AppConstants.errorColor;
        break;
      default:
        callIcon = Icons.call;
        callColor = AppConstants.secondaryTextColor;
    }

    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: 4,
      ),
      child: ListTile(
        leading: Icon(callIcon, color: callColor),
        title: Text(callLog.contactName ?? callLog.phoneNumber),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(callLog.phoneNumber),
            Text(
              _formatDateTime(callLog.timestamp),
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        trailing: IconButton(
          icon: const Icon(Icons.call, color: AppConstants.accentColor),
          onPressed: () => _makeCall(callLog.phoneNumber),
        ),
        onTap: () {
          setState(() {
            _phoneNumber = callLog.phoneNumber;
            _tabController.animateTo(0); // Switch to dialer tab
          });
        },
      ),
    );
  }

  String _getLetters(int number) {
    switch (number) {
      case 2: return 'ABC';
      case 3: return 'DEF';
      case 4: return 'GHI';
      case 5: return 'JKL';
      case 6: return 'MNO';
      case 7: return 'PQRS';
      case 8: return 'TUV';
      case 9: return 'WXYZ';
      default: return '';
    }
  }

  String _formatPhoneNumber(String phoneNumber) {
    final digits = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    if (digits.length <= 3) {
      return digits;
    } else if (digits.length <= 6) {
      return '${digits.substring(0, 3)}-${digits.substring(3)}';
    } else if (digits.length <= 10) {
      return '(${digits.substring(0, 3)}) ${digits.substring(3, 6)}-${digits.substring(6)}';
    } else {
      return '+${digits.substring(0, digits.length - 10)} (${digits.substring(digits.length - 10, digits.length - 7)}) ${digits.substring(digits.length - 7, digits.length - 4)}-${digits.substring(digits.length - 4)}';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  Future<void> _makeCall(String phoneNumber) async {
    try {
      final uri = Uri(scheme: 'tel', path: phoneNumber);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
        print('Call initiated to: $phoneNumber');
      } else {
        throw 'Could not launch $uri';
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Could not make call: $e'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }
}
