import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_theme.dart';
import '../providers/app_providers.dart';
import '../widgets/call_log_list_item.dart';
import '../widgets/banner_widget.dart';
import '../widgets/empty_state_widget.dart';

/// Recents tab showing call history
/// Includes banner and list of recent calls
class RecentsTab extends ConsumerWidget {
  const RecentsTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final callLogsAsync = ref.watch(callLogProvider);
    final searchQuery = ref.watch(searchProvider);
    final bannerVisibility = ref.watch(bannerVisibilityProvider);

    return Column(
      children: [
        // Caller ID Banner
        if (bannerVisibility['caller_id'] == true)
          BannerWidget(
            id: 'caller_id',
            icon: Icons.security,
            title: 'See who\'s calling',
            description: 'Enable caller ID to see who\'s calling before you answer. Protect yourself from spam.',
            actionText: 'Enable Caller ID',
            onAction: () {
              _showCallerIdDialog(context);
            },
            onClose: () {
              ref.read(bannerVisibilityProvider.notifier).hideBanner('caller_id');
            },
            backgroundColor: AppTheme.infoColor.withValues(alpha: 0.1),
            iconColor: AppTheme.infoColor,
          ),

        // Call logs list
        Expanded(
          child: callLogsAsync.when(
            data: (callLogs) {
              if (callLogs.isEmpty) {
                return const EmptyStateWidget(
                  icon: Icons.history,
                  title: 'No Recent Calls',
                  description: 'Your call history will appear here after you make or receive calls.',
                  actionText: 'Make a Call',
                );
              }

              // Filter call logs based on search query
              final filteredCallLogs = searchQuery.isEmpty
                  ? callLogs
                  : callLogs.where((callLog) {
                      final query = searchQuery.toLowerCase();
                      return (callLog.contactName?.toLowerCase().contains(query) ?? false) ||
                          callLog.phoneNumber.contains(searchQuery);
                    }).toList();

              if (filteredCallLogs.isEmpty && searchQuery.isNotEmpty) {
                return EmptyStateWidget(
                  icon: Icons.search_off,
                  title: 'No Results Found',
                  description: 'No call logs match "$searchQuery". Try a different search term.',
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.symmetric(vertical: AppTheme.spacing8),
                itemCount: filteredCallLogs.length,
                itemBuilder: (context, index) {
                  final callLog = filteredCallLogs[index];
                  return CallLogListItem(
                    callLog: callLog,
                    onTap: () => _showCallLogDetails(context, callLog.id),
                    onCall: () => _makeCall(context, ref, callLog.phoneNumber),
                    onMessage: () => _sendMessage(context, callLog.phoneNumber),
                    onDelete: () => _deleteCallLog(context, ref, callLog.id),
                  );
                },
              );
            },
            loading: () => const Center(
              child: CircularProgressIndicator(
                color: AppTheme.primaryGreen,
              ),
            ),
            error: (error, stack) => EmptyStateWidget(
              icon: Icons.error_outline,
              title: 'Error Loading Call History',
              description: 'Failed to load your call history. Please try again.',
              actionText: 'Retry',
              onAction: () {
                ref.read(callLogProvider.notifier).loadCallLogs();
              },
            ),
          ),
        ),
      ],
    );
  }

  void _showCallerIdDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
        ),
        title: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppTheme.infoColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              ),
              child: const Icon(
                Icons.security,
                color: AppTheme.infoColor,
                size: 20,
              ),
            ),
            const SizedBox(width: AppTheme.spacing12),
            const Text('Caller ID Protection'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Enable caller ID to get enhanced protection:',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: AppTheme.spacing16),
            _FeatureItem(
              icon: Icons.person,
              text: 'See caller name and photo',
            ),
            _FeatureItem(
              icon: Icons.block,
              text: 'Automatic spam detection',
            ),
            _FeatureItem(
              icon: Icons.location_on,
              text: 'Caller location information',
            ),
            _FeatureItem(
              icon: Icons.business,
              text: 'Business caller identification',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Not Now'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement caller ID
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Caller ID enabled!'),
                  backgroundColor: AppTheme.successColor,
                ),
              );
            },
            child: const Text('Enable'),
          ),
        ],
      ),
    );
  }

  void _showCallLogDetails(BuildContext context, String callLogId) {
    // TODO: Navigate to call log details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Call log details for $callLogId'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }

  void _makeCall(BuildContext context, WidgetRef ref, String phoneNumber) {
    // TODO: Implement actual call functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Calling $phoneNumber...'),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  void _sendMessage(BuildContext context, String phoneNumber) {
    // TODO: Implement messaging functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sending message to $phoneNumber...'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }

  void _deleteCallLog(BuildContext context, WidgetRef ref, String callLogId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Call Log'),
        content: const Text('Are you sure you want to delete this call log entry?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(callLogProvider.notifier).deleteCallLog(callLogId);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Call log deleted'),
                  backgroundColor: AppTheme.successColor,
                ),
              );
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

class _FeatureItem extends StatelessWidget {
  final IconData icon;
  final String text;

  const _FeatureItem({
    required this.icon,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppTheme.spacing4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: AppTheme.infoColor,
          ),
          const SizedBox(width: AppTheme.spacing8),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }
}
