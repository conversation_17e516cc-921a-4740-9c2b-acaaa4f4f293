import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_theme.dart';
import '../providers/app_providers.dart';
import '../widgets/contact_list_item.dart';
import '../widgets/banner_widget.dart';
import '../widgets/empty_state_widget.dart';

/// Favorites tab showing favorite contacts
/// Includes banner and quick access to favorite contacts
class FavoritesTab extends ConsumerWidget {
  const FavoritesTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final favoritesAsync = ref.watch(favoritesProvider);
    final searchQuery = ref.watch(searchProvider);
    final bannerVisibility = ref.watch(bannerVisibilityProvider);

    return Column(
      children: [
        // Contacts Enrichment Banner
        if (bannerVisibility['contacts_enrichment'] == true)
          BannerWidget(
            id: 'contacts_enrichment',
            icon: Icons.contact_phone,
            title: 'Contacts Enrichment',
            description: 'Get more information about your contacts with caller ID and social profiles.',
            actionText: 'Enable',
            onAction: () {
              _showContactsEnrichmentDialog(context);
            },
            onClose: () {
              ref.read(bannerVisibilityProvider.notifier).hideBanner('contacts_enrichment');
            },
          ),

        // Favorites list
        Expanded(
          child: favoritesAsync.when(
            data: (favorites) {
              if (favorites.isEmpty) {
                return const EmptyStateWidget(
                  icon: Icons.star_border,
                  title: 'No Favorites Yet',
                  description: 'Add contacts to favorites for quick access. Tap the star icon on any contact.',
                  actionText: 'Browse Contacts',
                );
              }

              // Filter favorites based on search query
              final filteredFavorites = searchQuery.isEmpty
                  ? favorites
                  : favorites.where((contact) {
                      final query = searchQuery.toLowerCase();
                      return contact.name.toLowerCase().contains(query) ||
                          contact.phoneNumbers.any((phone) => phone.number.contains(searchQuery));
                    }).toList();

              if (filteredFavorites.isEmpty && searchQuery.isNotEmpty) {
                return EmptyStateWidget(
                  icon: Icons.search_off,
                  title: 'No Results Found',
                  description: 'No favorites match "$searchQuery". Try a different search term.',
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.symmetric(vertical: AppTheme.spacing8),
                itemCount: filteredFavorites.length,
                itemBuilder: (context, index) {
                  final contact = filteredFavorites[index];
                  return ContactListItem(
                    contact: contact,
                    onTap: () => _showContactDetails(context, contact.id),
                    onCall: () => _makeCall(context, ref, contact.primaryPhoneNumber),
                    onMessage: () => _sendMessage(context, contact.primaryPhoneNumber),
                    showFavoriteButton: true,
                  );
                },
              );
            },
            loading: () => const Center(
              child: CircularProgressIndicator(
                color: AppTheme.primaryGreen,
              ),
            ),
            error: (error, stack) => EmptyStateWidget(
              icon: Icons.error_outline,
              title: 'Error Loading Favorites',
              description: 'Failed to load your favorite contacts. Please try again.',
              actionText: 'Retry',
              onAction: () {
                ref.read(favoritesProvider.notifier).loadFavorites();
              },
            ),
          ),
        ),
      ],
    );
  }

  void _showContactsEnrichmentDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
        ),
        title: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              ),
              child: const Icon(
                Icons.contact_phone,
                color: AppTheme.primaryGreen,
                size: 20,
              ),
            ),
            const SizedBox(width: AppTheme.spacing12),
            const Text('Contacts Enrichment'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Enhance your contacts with additional information:',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: AppTheme.spacing16),
            _FeatureItem(
              icon: Icons.person,
              text: 'Profile photos and social media links',
            ),
            _FeatureItem(
              icon: Icons.business,
              text: 'Company and job title information',
            ),
            _FeatureItem(
              icon: Icons.location_on,
              text: 'Location and address details',
            ),
            _FeatureItem(
              icon: Icons.security,
              text: 'Spam and fraud protection',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Not Now'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement contacts enrichment
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Contacts enrichment enabled!'),
                  backgroundColor: AppTheme.successColor,
                ),
              );
            },
            child: const Text('Enable'),
          ),
        ],
      ),
    );
  }

  void _showContactDetails(BuildContext context, String contactId) {
    // TODO: Navigate to contact details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Contact details for $contactId'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }

  void _makeCall(BuildContext context, WidgetRef ref, String? phoneNumber) {
    if (phoneNumber == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No phone number available'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    // TODO: Implement actual call functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Calling $phoneNumber...'),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  void _sendMessage(BuildContext context, String? phoneNumber) {
    if (phoneNumber == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No phone number available'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    // TODO: Implement messaging functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sending message to $phoneNumber...'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }
}

class _FeatureItem extends StatelessWidget {
  final IconData icon;
  final String text;

  const _FeatureItem({
    required this.icon,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppTheme.spacing4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: AppTheme.primaryGreen,
          ),
          const SizedBox(width: AppTheme.spacing8),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }
}
