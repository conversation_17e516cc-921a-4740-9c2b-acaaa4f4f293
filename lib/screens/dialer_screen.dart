import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/dial_pad.dart';
import '../constants/app_constants.dart';

class DialerScreen extends ConsumerStatefulWidget {
  const DialerScreen({super.key});

  @override
  ConsumerState<DialerScreen> createState() => _DialerScreenState();
}

class _DialerScreenState extends ConsumerState<DialerScreen> {
  String _phoneNumber = '';

  void _onNumberPressed(String number) {
    setState(() {
      _phoneNumber += number;
    });
  }

  void _onDeletePressed() {
    setState(() {
      if (_phoneNumber.isNotEmpty) {
        _phoneNumber = _phoneNumber.substring(0, _phoneNumber.length - 1);
      }
    });
  }

  void _onLongDelete() {
    setState(() {
      _phoneNumber = '';
    });
  }

  void _onCallPressed() {
    if (_phoneNumber.isNotEmpty) {
      // TODO: Implement call functionality
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Calling $_phoneNumber...'),
          backgroundColor: AppConstants.successColor,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: AppConstants.primaryColor,
        title: const Text('Dialer', style: TextStyle(color: Colors.white)),
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
      ),
      body: SafeArea(
        child: DialPad(
          phoneNumber: _phoneNumber,
          onNumberPressed: _onNumberPressed,
          onDeletePressed: _onDeletePressed,
          onLongDelete: _onLongDelete,
          onCallPressed: _onCallPressed,
        ),
      ),
    );
  }
}