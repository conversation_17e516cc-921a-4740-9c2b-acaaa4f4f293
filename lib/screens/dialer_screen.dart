import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import '../constants/app_theme.dart';
import '../models/call_log_model.dart';
import '../providers/app_providers.dart';

/// Dialer screen for Easy Dialer
/// Features a clean dial pad with T9 letters and call functionality
class DialerScreen extends ConsumerStatefulWidget {
  const DialerScreen({super.key});

  @override
  ConsumerState<DialerScreen> createState() => _DialerScreenState();
}

class _DialerScreenState extends ConsumerState<DialerScreen> {
  String _phoneNumber = '';
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Auto-focus for hardware keyboard input
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
        title: const Text('Dialer'),
        elevation: 0,
        actions: [
          // Add contact button
          IconButton(
            onPressed: _addContact,
            icon: const Icon(Icons.person_add),
            tooltip: 'Add Contact',
          ),
        ],
      ),
      body: Column(
        children: [
          // Phone number display
          _buildPhoneNumberDisplay(),
          
          // Dial pad
          Expanded(
            child: _buildDialPad(),
          ),
          
          // Call button
          _buildCallButton(),
          
          const SizedBox(height: AppTheme.spacing24),
        ],
      ),
    );
  }

  Widget _buildPhoneNumberDisplay() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.spacing24),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0x0A000000),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Phone number text field (hidden but functional for keyboard input)
          SizedBox(
            height: 0,
            child: TextField(
              focusNode: _focusNode,
              keyboardType: TextInputType.phone,
              onChanged: (value) {
                setState(() {
                  _phoneNumber = value;
                });
              },
              style: const TextStyle(fontSize: 0),
              decoration: const InputDecoration(
                border: InputBorder.none,
              ),
            ),
          ),
          
          // Phone number display
          Container(
            height: 60,
            alignment: Alignment.center,
            child: Text(
              _phoneNumber.isEmpty ? 'Enter phone number' : _formatPhoneNumber(_phoneNumber),
              style: TextStyle(
                fontSize: _phoneNumber.isEmpty ? 18 : 28,
                fontWeight: FontWeight.w400,
                color: _phoneNumber.isEmpty ? AppTheme.textSecondary : AppTheme.primaryGreen,
                letterSpacing: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          
          // Action buttons row
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Backspace button
              if (_phoneNumber.isNotEmpty)
                IconButton(
                  onPressed: _backspace,
                  onLongPress: _clearAll,
                  icon: const Icon(
                    Icons.backspace_outlined,
                    color: AppTheme.textSecondary,
                  ),
                  tooltip: 'Backspace (long press to clear)',
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDialPad() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing24),
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 1.0,
          crossAxisSpacing: AppTheme.dialPadSpacing,
          mainAxisSpacing: AppTheme.dialPadSpacing,
        ),
        itemCount: 12,
        itemBuilder: (context, index) {
          return _buildDialPadButton(index);
        },
      ),
    );
  }

  Widget _buildDialPadButton(int index) {
    String number;
    String? letters;
    
    if (index == 9) {
      number = '*';
      letters = null;
    } else if (index == 10) {
      number = '0';
      letters = '+';
    } else if (index == 11) {
      number = '#';
      letters = null;
    } else {
      number = '${index + 1}';
      letters = _getLettersForNumber(index + 1);
    }

    return Material(
      color: Colors.white,
      borderRadius: BorderRadius.circular(AppTheme.radiusCircular),
      elevation: 2,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      child: InkWell(
        borderRadius: BorderRadius.circular(AppTheme.radiusCircular),
        onTap: () => _onNumberPressed(number),
        onLongPress: index == 10 ? () => _onNumberPressed('+') : null,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppTheme.radiusCircular),
            border: Border.all(
              color: Colors.grey.shade200,
              width: 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                number,
                style: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.w400,
                  color: AppTheme.textPrimary,
                ),
              ),
              if (letters != null) ...[
                const SizedBox(height: 4),
                Text(
                  letters,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textSecondary,
                    letterSpacing: 1.5,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCallButton() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacing32),
      child: SizedBox(
        width: double.infinity,
        height: AppTheme.buttonHeightLarge,
        child: ElevatedButton.icon(
          onPressed: _phoneNumber.isNotEmpty ? _makeCall : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryGreen,
            foregroundColor: Colors.white,
            disabledBackgroundColor: AppTheme.textSecondary.withValues(alpha: 0.3),
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            ),
          ),
          icon: const Icon(Icons.call, size: 24),
          label: const Text(
            'Call',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  String? _getLettersForNumber(int number) {
    switch (number) {
      case 2: return 'ABC';
      case 3: return 'DEF';
      case 4: return 'GHI';
      case 5: return 'JKL';
      case 6: return 'MNO';
      case 7: return 'PQRS';
      case 8: return 'TUV';
      case 9: return 'WXYZ';
      default: return null;
    }
  }

  String _formatPhoneNumber(String number) {
    // Basic phone number formatting
    final cleaned = number.replaceAll(RegExp(r'[^\d+]'), '');
    if (cleaned.startsWith('+')) {
      return cleaned;
    }
    if (cleaned.length <= 3) {
      return cleaned;
    } else if (cleaned.length <= 6) {
      return '${cleaned.substring(0, 3)}-${cleaned.substring(3)}';
    } else if (cleaned.length <= 10) {
      return '(${cleaned.substring(0, 3)}) ${cleaned.substring(3, 6)}-${cleaned.substring(6)}';
    } else {
      return '+${cleaned.substring(0, cleaned.length - 10)} (${cleaned.substring(cleaned.length - 10, cleaned.length - 7)}) ${cleaned.substring(cleaned.length - 7, cleaned.length - 4)}-${cleaned.substring(cleaned.length - 4)}';
    }
  }

  void _onNumberPressed(String number) {
    HapticFeedback.lightImpact();
    setState(() {
      _phoneNumber += number;
    });
    
    // Update the hidden text field for keyboard input sync
    _focusNode.requestFocus();
  }

  void _backspace() {
    HapticFeedback.lightImpact();
    if (_phoneNumber.isNotEmpty) {
      setState(() {
        _phoneNumber = _phoneNumber.substring(0, _phoneNumber.length - 1);
      });
    }
  }

  void _clearAll() {
    HapticFeedback.mediumImpact();
    setState(() {
      _phoneNumber = '';
    });
  }

  Future<void> _makeCall() async {
    if (_phoneNumber.isEmpty) return;

    try {
      // Add haptic feedback
      HapticFeedback.heavyImpact();
      
      // Create call log entry
      final callLog = CallLogModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        phoneNumber: _phoneNumber,
        type: CallType.outgoing,
        status: CallStatus.connected,
        timestamp: DateTime.now(),
      );
      
      // Add to call log
      ref.read(callLogProvider.notifier).addCallLog(callLog);
      
      // Make the actual call
      final uri = Uri(scheme: 'tel', path: _phoneNumber);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
        
        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Calling $_phoneNumber...'),
              backgroundColor: AppTheme.successColor,
              duration: const Duration(seconds: 2),
            ),
          );
          
          // Clear the number after successful call
          setState(() {
            _phoneNumber = '';
          });
        }
      } else {
        throw Exception('Could not launch phone dialer');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to make call: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  void _addContact() {
    if (_phoneNumber.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Enter a phone number first'),
          backgroundColor: AppTheme.warningColor,
        ),
      );
      return;
    }

    // TODO: Implement add contact functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Add $_phoneNumber to contacts'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }
}
