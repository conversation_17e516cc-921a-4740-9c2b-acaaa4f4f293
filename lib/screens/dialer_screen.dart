import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../widgets/dial_pad.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/favorites_list.dart';
import '../widgets/recent_contacts_list.dart';
import '../providers/app_provider.dart';
import '../constants/app_constants.dart';

class DialerScreen extends ConsumerStatefulWidget {
  const DialerScreen({super.key});

  @override
  ConsumerState<DialerScreen> createState() => _DialerScreenState();
}

class _DialerScreenState extends ConsumerState<DialerScreen> {
  String _phoneNumber = '';
  bool _showDialPad = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: Text(
          AppConstants.appName,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(_showDialPad ? Icons.search : Icons.dialpad),
            onPressed: () {
              setState(() {
                _showDialPad = !_showDialPad;
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              _showMoreOptions(context);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          if (!_showDialPad)
            Container(
              margin: const EdgeInsets.all(AppConstants.paddingMedium),
              child: SearchBarWidget(
                onChanged: (query) {
                  ref.read(searchProvider.notifier).setSearchQuery(query);
                },
              ),
            ).animate().slideY(begin: -1, duration: AppConstants.animationMedium),

          // Phone Number Display
          Container(
            width: double.infinity,
            margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            decoration: BoxDecoration(
              color: AppConstants.surfaceColor,
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Text(
                  _phoneNumber.isEmpty ? 'Enter phone number' : _formatPhoneNumber(_phoneNumber),
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.w400,
                    color: _phoneNumber.isEmpty
                        ? AppConstants.secondaryTextColor
                        : AppConstants.primaryTextColor,
                    fontSize: 24,
                  ),
                  textAlign: TextAlign.center,
                ),
                if (_phoneNumber.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    'Tap to edit',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppConstants.secondaryTextColor,
                    ),
                  ).animate().fadeIn(),
                ],
              ],
            ),
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          // Content Area
          Expanded(
            child: _showDialPad ? _buildDialPadContent() : _buildSearchContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildDialPadContent() {
    return Column(
      children: [
        // Favorites Section
        Expanded(
          flex: 2,
          child: Consumer(
            builder: (context, ref, child) {
              final contactsAsync = ref.watch(contactProvider);
              return contactsAsync.when(
                data: (contacts) {
                  final favorites = ref.read(contactProvider.notifier).favorites;
                  if (favorites.isEmpty) {
                    return _buildEmptyState(
                      'No favorites yet',
                      'Add contacts to favorites for quick access',
                      Icons.favorite_border,
                    );
                  }
                  return FavoritesList(
                    favorites: favorites,
                    onContactTap: (contact) {
                      if (contact.phoneNumbers.isNotEmpty) {
                        _makeCall(contact.phoneNumbers.first);
                      }
                    },
                  );
                },
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => _buildEmptyState(
                  'Error loading contacts',
                  'Please check your permissions',
                  Icons.error_outline,
                ),
              );
            },
          ),
        ),

        // Recent Contacts Section
        Expanded(
          flex: 2,
          child: Consumer(
            builder: (context, ref, child) {
              final contactsAsync = ref.watch(contactProvider);
              return contactsAsync.when(
                data: (contacts) {
                  final recentContacts = ref.read(contactProvider.notifier).recentContacts;
                  if (recentContacts.isEmpty) {
                    return _buildEmptyState(
                      'No recent calls',
                      'Your recent calls will appear here',
                      Icons.history,
                    );
                  }
                  return RecentContactsList(
                    contacts: recentContacts,
                    onContactTap: (contact) {
                      if (contact.phoneNumbers.isNotEmpty) {
                        _makeCall(contact.phoneNumbers.first);
                      }
                    },
                  );
                },
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => _buildEmptyState(
                  'Error loading contacts',
                  'Please check your permissions',
                  Icons.error_outline,
                ),
              );
            },
          ),
        ),

        // Dial Pad
        Expanded(
          flex: 3,
          child: DialPad(
            onNumberPressed: (number) {
              setState(() {
                _phoneNumber += number;
              });
            },
            onDeletePressed: () {
              setState(() {
                if (_phoneNumber.isNotEmpty) {
                  _phoneNumber = _phoneNumber.substring(0, _phoneNumber.length - 1);
                }
              });
            },
            onCallPressed: () {
              if (_phoneNumber.isNotEmpty) {
                _makeCall(_phoneNumber);
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchContent() {
    final searchQuery = ref.watch(searchProvider);
    
    return Consumer(
      builder: (context, ref, child) {
        final contactsAsync = ref.watch(contactProvider);
        return contactsAsync.when(
          data: (contacts) {
            final searchResults = ref.read(contactProvider.notifier).searchContacts(searchQuery);
            if (searchResults.isEmpty && searchQuery.isNotEmpty) {
              return _buildEmptyState(
                'No contacts found',
                'Try searching with a different term',
                Icons.search_off,
              );
            }
            return ListView.builder(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              itemCount: searchResults.length,
              itemBuilder: (context, index) {
                final contact = searchResults[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: AppConstants.primaryColor,
                    child: Text(
                      contact.name.isNotEmpty ? contact.name[0].toUpperCase() : '?',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  title: Text(contact.name),
                  subtitle: contact.phoneNumbers.isNotEmpty 
                      ? Text(contact.phoneNumbers.first)
                      : null,
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.call),
                        onPressed: () {
                          if (contact.phoneNumbers.isNotEmpty) {
                            _makeCall(contact.phoneNumbers.first);
                          }
                        },
                      ),
                      IconButton(
                        icon: Icon(
                          contact.isFavorite ? Icons.favorite : Icons.favorite_border,
                          color: contact.isFavorite ? AppConstants.accentColor : null,
                        ),
                        onPressed: () {
                          if (contact.isFavorite) {
                            ref.read(contactProvider.notifier).removeFromFavorites(contact);
                          } else {
                            ref.read(contactProvider.notifier).addToFavorites(contact);
                          }
                        },
                      ),
                    ],
                  ),
                  onTap: () {
                    if (contact.phoneNumbers.isNotEmpty) {
                      _makeCall(contact.phoneNumbers.first);
                    }
                  },
                );
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildEmptyState(
            'Error loading contacts',
            'Please check your permissions',
            Icons.error_outline,
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(String title, String subtitle, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: AppConstants.secondaryTextColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppConstants.primaryTextColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _makeCall(String phoneNumber) async {
    try {
      await ref.read(callLogProvider.notifier).makeCall(phoneNumber);
      ref.read(currentCallProvider.notifier).startCall(phoneNumber);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Calling $phoneNumber...'),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to make call: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.block),
              title: const Text('Block Number'),
              onTap: () {
                Navigator.pop(context);
                _showBlockNumberDialog();
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Settings'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to settings
              },
            ),
            ListTile(
              leading: const Icon(Icons.help),
              title: const Text('Help'),
              onTap: () {
                Navigator.pop(context);
                // Show help
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showBlockNumberDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Block Number'),
        content: TextField(
          decoration: const InputDecoration(
            labelText: 'Enter phone number',
            hintText: '+**********',
          ),
          keyboardType: TextInputType.phone,
          onSubmitted: (number) {
            if (number.isNotEmpty) {
              ref.read(contactProvider.notifier).blockNumber(number);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('$number has been blocked'),
                  backgroundColor: AppConstants.successColor,
                ),
              );
            }
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // Handle block action
              Navigator.pop(context);
            },
            child: const Text('Block'),
          ),
        ],
      ),
    );
  }
} 