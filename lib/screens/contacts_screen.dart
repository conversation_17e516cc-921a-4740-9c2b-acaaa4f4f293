import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/search_bar_widget.dart';
import '../providers/app_provider.dart';
import '../constants/app_constants.dart';
import '../models/contact.dart';

class ContactsScreen extends ConsumerStatefulWidget {
  const ContactsScreen({super.key});

  @override
  ConsumerState<ContactsScreen> createState() => _ContactsScreenState();
}

class _ContactsScreenState extends ConsumerState<ContactsScreen> {
  String _searchQuery = '';

  @override
  Widget build(BuildContext context) {
    final contactsAsync = ref.watch(contactProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Contacts'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              // Add new contact
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              _showMoreOptions(context);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          SearchBarWidget(
            onChanged: (query) {
              setState(() {
                _searchQuery = query;
              });
            },
            hintText: 'Search contacts...',
          ),

          // Contacts List
          Expanded(
            child: contactsAsync.when(
              data: (contacts) {
                final filteredContacts = ref
                    .read(contactProvider.notifier)
                    .searchContacts(_searchQuery);
                
                if (filteredContacts.isEmpty) {
                  return _buildEmptyState();
                }

                return ListView.builder(
                  itemCount: filteredContacts.length,
                  itemBuilder: (context, index) {
                    final contact = filteredContacts[index];
                    return _buildContactTile(contact);
                  },
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stack) => _buildErrorState(error.toString()),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactTile(Contact contact) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppConstants.primaryColor,
          child: Text(
            contact.name.isNotEmpty ? contact.name[0].toUpperCase() : '?',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          contact.name,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: AppConstants.primaryTextColor,
          ),
        ),
        subtitle: contact.phoneNumbers.isNotEmpty
            ? Text(
                contact.phoneNumbers.first,
                style: const TextStyle(
                  color: AppConstants.secondaryTextColor,
                ),
              )
            : null,
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.call),
              onPressed: () {
                if (contact.phoneNumbers.isNotEmpty) {
                  _makeCall(contact.phoneNumbers.first);
                }
              },
              color: AppConstants.primaryColor,
            ),
            IconButton(
              icon: Icon(
                contact.isFavorite ? Icons.favorite : Icons.favorite_border,
                color: contact.isFavorite ? AppConstants.accentColor : null,
              ),
              onPressed: () {
                if (contact.isFavorite) {
                  ref.read(contactProvider.notifier).removeFromFavorites(contact);
                } else {
                  ref.read(contactProvider.notifier).addToFavorites(contact);
                }
              },
            ),
          ],
        ),
        onTap: () {
          if (contact.phoneNumbers.isNotEmpty) {
            _makeCall(contact.phoneNumbers.first);
          }
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.contacts_outlined,
            size: 64,
            color: AppConstants.secondaryTextColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'No contacts found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppConstants.primaryTextColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Add some contacts to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppConstants.errorColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'Error loading contacts',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppConstants.primaryTextColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          ElevatedButton(
            onPressed: () {
              ref.read(contactProvider.notifier).loadContacts();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Future<void> _makeCall(String phoneNumber) async {
    try {
      await ref.read(callLogProvider.notifier).makeCall(phoneNumber);
      ref.read(currentCallProvider.notifier).startCall(phoneNumber);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Calling $phoneNumber...'),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to make call: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.add),
              title: const Text('Add Contact'),
              onTap: () {
                Navigator.pop(context);
                // Add contact functionality
              },
            ),
            ListTile(
              leading: const Icon(Icons.import_export),
              title: const Text('Import Contacts'),
              onTap: () {
                Navigator.pop(context);
                // Import contacts functionality
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Contact Settings'),
              onTap: () {
                Navigator.pop(context);
                // Contact settings
              },
            ),
          ],
        ),
      ),
    );
  }
} 