import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_theme.dart';
import '../providers/app_providers.dart';
import '../screens/favorites_tab.dart';
import '../screens/recents_tab.dart';
import '../screens/contacts_tab.dart';
import '../screens/dialer_screen.dart';
import '../screens/settings_screen.dart';
import '../widgets/search_app_bar.dart';

/// Main screen with TabBar navigation
/// Implements the Easy Dialer layout with Favorites, Recents, and Contacts tabs
class MainScreen extends ConsumerStatefulWidget {
  const MainScreen({super.key});

  @override
  ConsumerState<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends ConsumerState<MainScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // Listen to tab changes
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        ref.read(currentTabProvider.notifier).setTab(_tabController.index);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentTab = ref.watch(currentTabProvider);
    
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          // Tab bar
          _buildTabBar(),
          
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: const [
                FavoritesTab(),
                RecentsTab(),
                ContactsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  PreferredSizeWidget _buildAppBar() {
    if (_isSearching) {
      return SearchAppBar(
        onSearchChanged: (query) {
          ref.read(searchProvider.notifier).updateQuery(query);
        },
        onSearchClosed: () {
          setState(() {
            _isSearching = false;
          });
          ref.read(searchProvider.notifier).clearQuery();
        },
      );
    }

    return AppBar(
      backgroundColor: AppTheme.primaryGreen,
      foregroundColor: Colors.white,
      elevation: 0,
      title: const Text(
        'Easy Dialer',
        style: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
      actions: [
        // Search button
        IconButton(
          onPressed: () {
            setState(() {
              _isSearching = true;
            });
          },
          icon: const Icon(Icons.search),
          tooltip: 'Search',
        ),
        
        // Add contact button
        IconButton(
          onPressed: _showAddContactDialog,
          icon: const Icon(Icons.person_add),
          tooltip: 'Add Contact',
        ),
        
        // More menu
        PopupMenuButton<String>(
          onSelected: _handleMenuSelection,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'settings',
              child: ListTile(
                leading: Icon(Icons.settings),
                title: Text('Settings'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'blocked',
              child: ListTile(
                leading: Icon(Icons.block),
                title: Text('Blocked Numbers'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'backup',
              child: ListTile(
                leading: Icon(Icons.backup),
                title: Text('Backup & Restore'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'about',
              child: ListTile(
                leading: Icon(Icons.info),
                title: Text('About'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
          icon: const Icon(Icons.more_vert),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppTheme.primaryGreen,
      child: TabBar(
        controller: _tabController,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        indicatorColor: Colors.white,
        indicatorWeight: 3,
        labelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
        tabs: const [
          Tab(
            text: 'Favorites',
            icon: Icon(Icons.star, size: 20),
          ),
          Tab(
            text: 'Recents',
            icon: Icon(Icons.history, size: 20),
          ),
          Tab(
            text: 'Contacts',
            icon: Icon(Icons.contacts, size: 20),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _openDialer,
      backgroundColor: AppTheme.primaryGreen,
      foregroundColor: Colors.white,
      elevation: 8,
      icon: const Icon(Icons.dialpad, size: 24),
      label: const Text(
        'Dial',
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  void _handleMenuSelection(String value) {
    switch (value) {
      case 'settings':
        _openSettings();
        break;
      case 'blocked':
        _showBlockedNumbers();
        break;
      case 'backup':
        _showBackupDialog();
        break;
      case 'about':
        _showAboutDialog();
        break;
    }
  }

  void _openDialer() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const DialerScreen(),
        fullscreenDialog: true,
      ),
    );
  }

  void _openSettings() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SettingsScreen(),
      ),
    );
  }

  void _showAddContactDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Contact'),
        content: const Text('Add contact functionality will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement add contact
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _showBlockedNumbers() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Blocked Numbers'),
        content: const Text('Blocked numbers management will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showBackupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Backup & Restore'),
        content: const Text('Backup and restore functionality will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: 'Easy Dialer',
      applicationVersion: '1.0.0',
      applicationIcon: Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: AppTheme.primaryGreen,
          borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        ),
        child: const Icon(
          Icons.phone,
          color: Colors.white,
          size: 32,
        ),
      ),
      children: [
        const Text('A beautiful and feature-rich dialer app with caller ID and spam protection.'),
        const SizedBox(height: 16),
        const Text('Built with Flutter and Material Design 3.'),
      ],
    );
  }
}
