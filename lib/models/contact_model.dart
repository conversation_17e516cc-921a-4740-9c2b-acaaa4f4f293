import 'package:flutter/material.dart';

/// Contact model representing a contact in the Easy Dialer app
class ContactModel {
  final String id;
  final String name;
  final String? displayName;
  final List<PhoneNumber> phoneNumbers;
  final List<EmailAddress> emailAddresses;
  final String? photoUrl;
  final String? company;
  final String? jobTitle;
  final bool isFavorite;
  final bool isBlocked;
  final DateTime? lastContactTime;
  final int callCount;
  final String? notes;
  final List<String> tags;

  const ContactModel({
    required this.id,
    required this.name,
    this.displayName,
    required this.phoneNumbers,
    this.emailAddresses = const [],
    this.photoUrl,
    this.company,
    this.jobTitle,
    this.isFavorite = false,
    this.isBlocked = false,
    this.lastContactTime,
    this.callCount = 0,
    this.notes,
    this.tags = const [],
  });

  /// Get the primary phone number
  String? get primaryPhoneNumber {
    if (phoneNumbers.isEmpty) return null;
    
    // Try to find mobile number first
    final mobile = phoneNumbers.where((p) => p.type == PhoneNumberType.mobile).firstOrNull;
    if (mobile != null) return mobile.number;
    
    // Otherwise return the first number
    return phoneNumbers.first.number;
  }

  /// Get the primary email address
  String? get primaryEmail {
    if (emailAddresses.isEmpty) return null;
    return emailAddresses.first.address;
  }

  /// Get display name or fallback to name
  String get effectiveDisplayName => displayName ?? name;

  /// Get initials for avatar
  String get initials {
    final words = effectiveDisplayName.trim().split(' ');
    if (words.isEmpty) return '?';
    if (words.length == 1) {
      return words[0].isNotEmpty ? words[0][0].toUpperCase() : '?';
    }
    return '${words[0][0]}${words[1][0]}'.toUpperCase();
  }

  /// Get avatar color based on name
  Color get avatarColor {
    final colors = [
      Colors.red.shade400,
      Colors.pink.shade400,
      Colors.purple.shade400,
      Colors.deepPurple.shade400,
      Colors.indigo.shade400,
      Colors.blue.shade400,
      Colors.lightBlue.shade400,
      Colors.cyan.shade400,
      Colors.teal.shade400,
      Colors.green.shade400,
      Colors.lightGreen.shade400,
      Colors.lime.shade400,
      Colors.yellow.shade400,
      Colors.amber.shade400,
      Colors.orange.shade400,
      Colors.deepOrange.shade400,
    ];
    
    final hash = name.hashCode;
    return colors[hash.abs() % colors.length];
  }

  /// Create a copy with updated fields
  ContactModel copyWith({
    String? id,
    String? name,
    String? displayName,
    List<PhoneNumber>? phoneNumbers,
    List<EmailAddress>? emailAddresses,
    String? photoUrl,
    String? company,
    String? jobTitle,
    bool? isFavorite,
    bool? isBlocked,
    DateTime? lastContactTime,
    int? callCount,
    String? notes,
    List<String>? tags,
  }) {
    return ContactModel(
      id: id ?? this.id,
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      phoneNumbers: phoneNumbers ?? this.phoneNumbers,
      emailAddresses: emailAddresses ?? this.emailAddresses,
      photoUrl: photoUrl ?? this.photoUrl,
      company: company ?? this.company,
      jobTitle: jobTitle ?? this.jobTitle,
      isFavorite: isFavorite ?? this.isFavorite,
      isBlocked: isBlocked ?? this.isBlocked,
      lastContactTime: lastContactTime ?? this.lastContactTime,
      callCount: callCount ?? this.callCount,
      notes: notes ?? this.notes,
      tags: tags ?? this.tags,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'displayName': displayName,
      'phoneNumbers': phoneNumbers.map((p) => p.toJson()).toList(),
      'emailAddresses': emailAddresses.map((e) => e.toJson()).toList(),
      'photoUrl': photoUrl,
      'company': company,
      'jobTitle': jobTitle,
      'isFavorite': isFavorite,
      'isBlocked': isBlocked,
      'lastContactTime': lastContactTime?.toIso8601String(),
      'callCount': callCount,
      'notes': notes,
      'tags': tags,
    };
  }

  /// Create from JSON
  factory ContactModel.fromJson(Map<String, dynamic> json) {
    return ContactModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      displayName: json['displayName'],
      phoneNumbers: (json['phoneNumbers'] as List<dynamic>?)
          ?.map((p) => PhoneNumber.fromJson(p as Map<String, dynamic>))
          .toList() ?? [],
      emailAddresses: (json['emailAddresses'] as List<dynamic>?)
          ?.map((e) => EmailAddress.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      photoUrl: json['photoUrl'],
      company: json['company'],
      jobTitle: json['jobTitle'],
      isFavorite: json['isFavorite'] ?? false,
      isBlocked: json['isBlocked'] ?? false,
      lastContactTime: json['lastContactTime'] != null
          ? DateTime.parse(json['lastContactTime'])
          : null,
      callCount: json['callCount'] ?? 0,
      notes: json['notes'],
      tags: (json['tags'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ContactModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ContactModel(id: $id, name: $name, phoneNumbers: ${phoneNumbers.length})';
  }
}

/// Phone number with type information
class PhoneNumber {
  final String number;
  final PhoneNumberType type;
  final String? label;

  const PhoneNumber({
    required this.number,
    required this.type,
    this.label,
  });

  /// Get formatted phone number
  String get formattedNumber {
    // Basic formatting - can be enhanced with phone number formatting library
    final cleaned = number.replaceAll(RegExp(r'[^\d+]'), '');
    if (cleaned.length == 10) {
      return '(${cleaned.substring(0, 3)}) ${cleaned.substring(3, 6)}-${cleaned.substring(6)}';
    }
    return cleaned;
  }

  /// Get type display name
  String get typeDisplayName {
    switch (type) {
      case PhoneNumberType.mobile:
        return 'Mobile';
      case PhoneNumberType.home:
        return 'Home';
      case PhoneNumberType.work:
        return 'Work';
      case PhoneNumberType.other:
        return label ?? 'Other';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'type': type.name,
      'label': label,
    };
  }

  factory PhoneNumber.fromJson(Map<String, dynamic> json) {
    return PhoneNumber(
      number: json['number'] ?? '',
      type: PhoneNumberType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => PhoneNumberType.other,
      ),
      label: json['label'],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PhoneNumber && other.number == number && other.type == type;
  }

  @override
  int get hashCode => Object.hash(number, type);
}

/// Email address with type information
class EmailAddress {
  final String address;
  final EmailAddressType type;
  final String? label;

  const EmailAddress({
    required this.address,
    required this.type,
    this.label,
  });

  /// Get type display name
  String get typeDisplayName {
    switch (type) {
      case EmailAddressType.personal:
        return 'Personal';
      case EmailAddressType.work:
        return 'Work';
      case EmailAddressType.other:
        return label ?? 'Other';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'type': type.name,
      'label': label,
    };
  }

  factory EmailAddress.fromJson(Map<String, dynamic> json) {
    return EmailAddress(
      address: json['address'] ?? '',
      type: EmailAddressType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => EmailAddressType.other,
      ),
      label: json['label'],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EmailAddress && other.address == address && other.type == type;
  }

  @override
  int get hashCode => Object.hash(address, type);
}

/// Phone number types
enum PhoneNumberType {
  mobile,
  home,
  work,
  other,
}

/// Email address types
enum EmailAddressType {
  personal,
  work,
  other,
}
