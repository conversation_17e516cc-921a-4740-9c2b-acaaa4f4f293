import 'package:flutter/material.dart';
import '../constants/app_theme.dart';

/// Call log model representing a call entry in the Easy Dialer app
class CallLogModel {
  final String id;
  final String phoneNumber;
  final String? contactName;
  final String? contactId;
  final CallType type;
  final CallStatus status;
  final DateTime timestamp;
  final Duration? duration;
  final String? photoUrl;
  final bool isBlocked;
  final bool isSpam;
  final String? location;
  final String? notes;

  const CallLogModel({
    required this.id,
    required this.phoneNumber,
    this.contactName,
    this.contactId,
    required this.type,
    required this.status,
    required this.timestamp,
    this.duration,
    this.photoUrl,
    this.isBlocked = false,
    this.isSpam = false,
    this.location,
    this.notes,
  });

  /// Get display name (contact name or formatted phone number)
  String get displayName {
    if (contactName != null && contactName!.isNotEmpty) {
      return contactName!;
    }
    return formattedPhoneNumber;
  }

  /// Get formatted phone number
  String get formattedPhoneNumber {
    // Basic formatting - can be enhanced with phone number formatting library
    final cleaned = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    if (cleaned.length == 10) {
      return '(${cleaned.substring(0, 3)}) ${cleaned.substring(3, 6)}-${cleaned.substring(6)}';
    }
    return cleaned;
  }

  /// Get call type icon
  IconData get typeIcon {
    switch (type) {
      case CallType.incoming:
        return Icons.call_received;
      case CallType.outgoing:
        return Icons.call_made;
      case CallType.missed:
        return Icons.call_received;
      case CallType.rejected:
        return Icons.call_end;
      case CallType.blocked:
        return Icons.block;
    }
  }

  /// Get call type color
  Color get typeColor {
    switch (type) {
      case CallType.incoming:
        return AppTheme.incomingCallColor;
      case CallType.outgoing:
        return AppTheme.outgoingCallColor;
      case CallType.missed:
      case CallType.rejected:
        return AppTheme.missedCallColor;
      case CallType.blocked:
        return AppTheme.errorColor;
    }
  }

  /// Get call type display name
  String get typeDisplayName {
    switch (type) {
      case CallType.incoming:
        return 'Incoming';
      case CallType.outgoing:
        return 'Outgoing';
      case CallType.missed:
        return 'Missed';
      case CallType.rejected:
        return 'Rejected';
      case CallType.blocked:
        return 'Blocked';
    }
  }

  /// Get call status display name
  String get statusDisplayName {
    switch (status) {
      case CallStatus.connected:
        return 'Connected';
      case CallStatus.busy:
        return 'Busy';
      case CallStatus.noAnswer:
        return 'No Answer';
      case CallStatus.failed:
        return 'Failed';
      case CallStatus.blocked:
        return 'Blocked';
      case CallStatus.rejected:
        return 'Rejected';
    }
  }

  /// Get formatted duration
  String get formattedDuration {
    if (duration == null) return '';
    
    final totalSeconds = duration!.inSeconds;
    final hours = totalSeconds ~/ 3600;
    final minutes = (totalSeconds % 3600) ~/ 60;
    final seconds = totalSeconds % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m ${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  /// Get relative time string
  String get relativeTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return 'Yesterday';
      } else if (difference.inDays < 7) {
        return '${difference.inDays} days ago';
      } else {
        return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
      }
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  /// Get formatted time
  String get formattedTime {
    final hour = timestamp.hour;
    final minute = timestamp.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    return '$displayHour:$minute $period';
  }

  /// Get initials for avatar (if no contact name)
  String get initials {
    if (contactName != null && contactName!.isNotEmpty) {
      final words = contactName!.trim().split(' ');
      if (words.isEmpty) return '?';
      if (words.length == 1) {
        return words[0].isNotEmpty ? words[0][0].toUpperCase() : '?';
      }
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    }
    
    // Use phone number for initials
    final cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    if (cleaned.length >= 2) {
      return cleaned.substring(cleaned.length - 2);
    }
    return '?';
  }

  /// Get avatar color based on phone number
  Color get avatarColor {
    final colors = [
      Colors.red.shade400,
      Colors.pink.shade400,
      Colors.purple.shade400,
      Colors.deepPurple.shade400,
      Colors.indigo.shade400,
      Colors.blue.shade400,
      Colors.lightBlue.shade400,
      Colors.cyan.shade400,
      Colors.teal.shade400,
      Colors.green.shade400,
      Colors.lightGreen.shade400,
      Colors.lime.shade400,
      Colors.yellow.shade400,
      Colors.amber.shade400,
      Colors.orange.shade400,
      Colors.deepOrange.shade400,
    ];
    
    final hash = phoneNumber.hashCode;
    return colors[hash.abs() % colors.length];
  }

  /// Create a copy with updated fields
  CallLogModel copyWith({
    String? id,
    String? phoneNumber,
    String? contactName,
    String? contactId,
    CallType? type,
    CallStatus? status,
    DateTime? timestamp,
    Duration? duration,
    String? photoUrl,
    bool? isBlocked,
    bool? isSpam,
    String? location,
    String? notes,
  }) {
    return CallLogModel(
      id: id ?? this.id,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      contactName: contactName ?? this.contactName,
      contactId: contactId ?? this.contactId,
      type: type ?? this.type,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      duration: duration ?? this.duration,
      photoUrl: photoUrl ?? this.photoUrl,
      isBlocked: isBlocked ?? this.isBlocked,
      isSpam: isSpam ?? this.isSpam,
      location: location ?? this.location,
      notes: notes ?? this.notes,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'phoneNumber': phoneNumber,
      'contactName': contactName,
      'contactId': contactId,
      'type': type.name,
      'status': status.name,
      'timestamp': timestamp.toIso8601String(),
      'duration': duration?.inSeconds,
      'photoUrl': photoUrl,
      'isBlocked': isBlocked,
      'isSpam': isSpam,
      'location': location,
      'notes': notes,
    };
  }

  /// Create from JSON
  factory CallLogModel.fromJson(Map<String, dynamic> json) {
    return CallLogModel(
      id: json['id'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      contactName: json['contactName'],
      contactId: json['contactId'],
      type: CallType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => CallType.outgoing,
      ),
      status: CallStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => CallStatus.connected,
      ),
      timestamp: DateTime.parse(json['timestamp']),
      duration: json['duration'] != null ? Duration(seconds: json['duration']) : null,
      photoUrl: json['photoUrl'],
      isBlocked: json['isBlocked'] ?? false,
      isSpam: json['isSpam'] ?? false,
      location: json['location'],
      notes: json['notes'],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CallLogModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CallLogModel(id: $id, phoneNumber: $phoneNumber, type: $type, timestamp: $timestamp)';
  }
}

/// Call types
enum CallType {
  incoming,
  outgoing,
  missed,
  rejected,
  blocked,
}

/// Call status
enum CallStatus {
  connected,
  busy,
  noAnswer,
  failed,
  blocked,
  rejected,
}
