import 'package:flutter/material.dart';

class AppConstants {
  // App Info
  static const String appName = 'Lite Dialer';
  static const String appVersion = '1.0.0';
  
  // Modern Colors
  static const Color primaryColor = Color(0xFF1976D2);
  static const Color secondaryColor = Color(0xFF00BCD4);
  static const Color accentColor = Color(0xFF4CAF50);
  static const Color backgroundColor = Color(0xFFF8F9FA);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color errorColor = Color(0xFFE53E3E);
  static const Color successColor = Color(0xFF38A169);
  static const Color warningColor = Color(0xFFED8936);
  
  // Call Status Colors
  static const Color incomingCallColor = Color(0xFF4CAF50);
  static const Color outgoingCallColor = Color(0xFF2196F3);
  static const Color missedCallColor = Color(0xFFFF5722);
  static const Color blockedCallColor = Color(0xFF9C27B0);
  
  // Text Colors
  static const Color primaryTextColor = Color(0xFF212121);
  static const Color secondaryTextColor = Color(0xFF757575);
  static const Color disabledTextColor = Color(0xFFBDBDBD);
  
  // Spacing
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  
  // Border Radius
  static const double borderRadiusSmall = 8.0;
  static const double borderRadiusMedium = 12.0;
  static const double borderRadiusLarge = 16.0;
  static const double borderRadiusXLarge = 24.0;
  
  // Button Sizes
  static const double buttonHeight = 56.0;
  static const double buttonWidth = 56.0;
  static const double dialPadButtonSize = 72.0;
  
  // Animation Durations
  static const Duration animationFast = Duration(milliseconds: 200);
  static const Duration animationMedium = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);
  
  // API Endpoints (for caller ID lookup)
  static const String callerIdApiBase = 'https://api.callerid.com';
  
  // Storage Keys
  static const String themeKey = 'theme_mode';
  static const String blockedNumbersKey = 'blocked_numbers';
  static const String favoritesKey = 'favorite_contacts';
  static const String settingsKey = 'app_settings';
  
  // Permission Messages
  static const String phonePermissionMessage = 'Phone permission is required to make calls';
  static const String contactsPermissionMessage = 'Contacts permission is required to access your contacts';
  static const String callLogPermissionMessage = 'Call log permission is required to view call history';
  
  // Error Messages
  static const String networkError = 'Network error. Please check your connection.';
  static const String permissionDenied = 'Permission denied. Please grant required permissions.';
  static const String unknownError = 'An unknown error occurred.';
  
  // Success Messages
  static const String callBlocked = 'Call blocked successfully';
  static const String contactAdded = 'Contact added to favorites';
  static const String contactRemoved = 'Contact removed from favorites';
  
  // T9 Mapping
  static const Map<String, List<String>> t9Mapping = {
    '2': ['a', 'b', 'c'],
    '3': ['d', 'e', 'f'],
    '4': ['g', 'h', 'i'],
    '5': ['j', 'k', 'l'],
    '6': ['m', 'n', 'o'],
    '7': ['p', 'q', 'r', 's'],
    '8': ['t', 'u', 'v'],
    '9': ['w', 'x', 'y', 'z'],
  };
} 