import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_theme.dart';
import '../models/contact_model.dart';
import '../providers/app_providers.dart';

/// Contact list item widget for Easy Dialer
/// Displays contact information with action buttons
class ContactListItem extends ConsumerWidget {
  final ContactModel contact;
  final VoidCallback? onTap;
  final VoidCallback? onCall;
  final VoidCallback? onMessage;
  final VoidCallback? onVideoCall;
  final bool showFavoriteButton;
  final bool showCallButton;
  final bool showMessageButton;
  final bool showVideoCallButton;

  const ContactListItem({
    super.key,
    required this.contact,
    this.onTap,
    this.onCall,
    this.onMessage,
    this.onVideoCall,
    this.showFavoriteButton = true,
    this.showCallButton = true,
    this.showMessageButton = false,
    this.showVideoCallButton = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing16,
        vertical: AppTheme.spacing4,
      ),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacing16),
          child: Row(
            children: [
              // Avatar
              _buildAvatar(),
              
              const SizedBox(width: AppTheme.spacing16),
              
              // Contact info
              Expanded(
                child: _buildContactInfo(),
              ),
              
              // Action buttons
              _buildActionButtons(ref),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: AppTheme.avatarLarge,
      height: AppTheme.avatarLarge,
      decoration: BoxDecoration(
        color: contact.photoUrl != null ? null : contact.avatarColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
        image: contact.photoUrl != null
            ? DecorationImage(
                image: NetworkImage(contact.photoUrl!),
                fit: BoxFit.cover,
              )
            : null,
      ),
      child: contact.photoUrl == null
          ? Center(
              child: Text(
                contact.initials,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildContactInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Name
        Text(
          contact.effectiveDisplayName,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        
        const SizedBox(height: 4),
        
        // Phone number
        if (contact.primaryPhoneNumber != null)
          Text(
            contact.primaryPhoneNumber!,
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondary,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        
        // Company/Job title
        if (contact.company != null || contact.jobTitle != null) ...[
          const SizedBox(height: 2),
          Text(
            [contact.jobTitle, contact.company]
                .where((e) => e != null && e.isNotEmpty)
                .join(' at '),
            style: const TextStyle(
              fontSize: 12,
              color: AppTheme.textSecondary,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  Widget _buildActionButtons(WidgetRef ref) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Favorite button
        if (showFavoriteButton)
          IconButton(
            onPressed: () => _toggleFavorite(ref),
            icon: Icon(
              contact.isFavorite ? Icons.star : Icons.star_border,
              color: contact.isFavorite ? AppTheme.warningColor : AppTheme.textSecondary,
              size: 20,
            ),
            tooltip: contact.isFavorite ? 'Remove from favorites' : 'Add to favorites',
            padding: const EdgeInsets.all(8),
            constraints: const BoxConstraints(
              minWidth: 36,
              minHeight: 36,
            ),
          ),
        
        // Message button
        if (showMessageButton && contact.primaryPhoneNumber != null)
          IconButton(
            onPressed: onMessage,
            icon: const Icon(
              Icons.message,
              color: AppTheme.textSecondary,
              size: 20,
            ),
            tooltip: 'Send message',
            padding: const EdgeInsets.all(8),
            constraints: const BoxConstraints(
              minWidth: 36,
              minHeight: 36,
            ),
          ),
        
        // Video call button
        if (showVideoCallButton && contact.primaryPhoneNumber != null)
          IconButton(
            onPressed: onVideoCall,
            icon: const Icon(
              Icons.videocam,
              color: AppTheme.textSecondary,
              size: 20,
            ),
            tooltip: 'Video call',
            padding: const EdgeInsets.all(8),
            constraints: const BoxConstraints(
              minWidth: 36,
              minHeight: 36,
            ),
          ),
        
        // Call button
        if (showCallButton && contact.primaryPhoneNumber != null)
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: AppTheme.primaryGreen,
              borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            ),
            child: IconButton(
              onPressed: onCall,
              icon: const Icon(
                Icons.call,
                color: Colors.white,
                size: 18,
              ),
              tooltip: 'Call',
              padding: EdgeInsets.zero,
            ),
          ),
      ],
    );
  }

  void _toggleFavorite(WidgetRef ref) {
    if (contact.isFavorite) {
      ref.read(favoritesProvider.notifier).removeFromFavorites(contact.id);
    } else {
      ref.read(favoritesProvider.notifier).addToFavorites(contact);
    }
  }
}

/// Compact contact list item for smaller spaces
class CompactContactListItem extends ConsumerWidget {
  final ContactModel contact;
  final VoidCallback? onTap;
  final VoidCallback? onCall;

  const CompactContactListItem({
    super.key,
    required this.contact,
    this.onTap,
    this.onCall,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ListTile(
      onTap: onTap,
      leading: Container(
        width: AppTheme.avatarMedium,
        height: AppTheme.avatarMedium,
        decoration: BoxDecoration(
          color: contact.photoUrl != null ? null : contact.avatarColor,
          borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          image: contact.photoUrl != null
              ? DecorationImage(
                  image: NetworkImage(contact.photoUrl!),
                  fit: BoxFit.cover,
                )
              : null,
        ),
        child: contact.photoUrl == null
            ? Center(
                child: Text(
                  contact.initials,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              )
            : null,
      ),
      title: Text(
        contact.effectiveDisplayName,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: contact.primaryPhoneNumber != null
          ? Text(
              contact.primaryPhoneNumber!,
              style: const TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondary,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            )
          : null,
      trailing: contact.primaryPhoneNumber != null
          ? IconButton(
              onPressed: onCall,
              icon: const Icon(
                Icons.call,
                color: AppTheme.primaryGreen,
                size: 20,
              ),
              tooltip: 'Call',
            )
          : null,
    );
  }
}
