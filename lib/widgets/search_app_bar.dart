import 'package:flutter/material.dart';
import '../constants/app_theme.dart';

/// Search app bar for Easy Dialer
/// Provides search functionality with a clean, modern design
class SearchAppBar extends StatefulWidget implements PreferredSizeWidget {
  final Function(String) onSearchChanged;
  final VoidCallback onSearchClosed;
  final String? hintText;

  const SearchAppBar({
    super.key,
    required this.onSearchChanged,
    required this.onSearchClosed,
    this.hintText,
  });

  @override
  State<SearchAppBar> createState() => _SearchAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _SearchAppBarState extends State<SearchAppBar> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Auto-focus when the search bar appears
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppTheme.primaryGreen,
      foregroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        onPressed: widget.onSearchClosed,
        icon: const Icon(Icons.arrow_back),
        tooltip: 'Close search',
      ),
      title: _buildSearchField(),
      actions: [
        // Clear search button
        if (_controller.text.isNotEmpty)
          IconButton(
            onPressed: _clearSearch,
            icon: const Icon(Icons.clear),
            tooltip: 'Clear search',
          ),
      ],
    );
  }

  Widget _buildSearchField() {
    return TextField(
      controller: _controller,
      focusNode: _focusNode,
      style: const TextStyle(
        color: Colors.white,
        fontSize: 16,
      ),
      decoration: InputDecoration(
        hintText: widget.hintText ?? 'Search contacts, numbers...',
        hintStyle: TextStyle(
          color: Colors.white.withValues(alpha: 0.7),
          fontSize: 16,
        ),
        border: InputBorder.none,
        enabledBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        contentPadding: EdgeInsets.zero,
      ),
      textInputAction: TextInputAction.search,
      onChanged: (value) {
        setState(() {}); // Rebuild to show/hide clear button
        widget.onSearchChanged(value);
      },
      onSubmitted: (value) {
        widget.onSearchChanged(value);
      },
    );
  }

  void _clearSearch() {
    _controller.clear();
    widget.onSearchChanged('');
    setState(() {});
    _focusNode.requestFocus();
  }
}
