import 'package:flutter/material.dart';
import '../constants/app_theme.dart';

/// Banner widget for Easy Dialer
/// Shows promotional banners with icon, title, description, and actions
class BannerWidget extends StatelessWidget {
  final String id;
  final IconData icon;
  final String title;
  final String description;
  final String? actionText;
  final VoidCallback? onAction;
  final VoidCallback? onClose;
  final Color? backgroundColor;
  final Color? iconColor;

  const BannerWidget({
    super.key,
    required this.id,
    required this.icon,
    required this.title,
    required this.description,
    this.actionText,
    this.onAction,
    this.onClose,
    this.backgroundColor,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacing16),
      padding: const EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppTheme.primaryGreen.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(
          color: (backgroundColor ?? AppTheme.primaryGreen).withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon, title, and close button
          Row(
            children: [
              // Icon
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: iconColor ?? AppTheme.primaryGreen,
                  borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                ),
                child: Icon(
                  icon,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              
              const SizedBox(width: AppTheme.spacing12),
              
              // Title
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
              ),
              
              // Close button
              if (onClose != null)
                IconButton(
                  onPressed: onClose,
                  icon: const Icon(
                    Icons.close,
                    size: 20,
                    color: AppTheme.textSecondary,
                  ),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: AppTheme.spacing12),
          
          // Description
          Text(
            description,
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondary,
              height: 1.4,
            ),
          ),
          
          // Action button
          if (actionText != null && onAction != null) ...[
            const SizedBox(height: AppTheme.spacing16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onAction,
                style: ElevatedButton.styleFrom(
                  backgroundColor: iconColor ?? AppTheme.primaryGreen,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: AppTheme.spacing12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                  ),
                ),
                child: Text(
                  actionText!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Predefined banner configurations for Easy Dialer
class BannerConfigs {
  static const contactsEnrichment = BannerConfig(
    id: 'contacts_enrichment',
    icon: Icons.contact_phone,
    title: 'Contacts Enrichment',
    description: 'Get more information about your contacts with caller ID and social profiles.',
    actionText: 'Enable',
    backgroundColor: AppTheme.primaryGreen,
  );

  static const callerIdProtection = BannerConfig(
    id: 'caller_id',
    icon: Icons.security,
    title: 'See who\'s calling',
    description: 'Enable caller ID to see who\'s calling before you answer. Protect yourself from spam.',
    actionText: 'Enable Caller ID',
    backgroundColor: AppTheme.infoColor,
  );

  static const spamProtection = BannerConfig(
    id: 'spam_protection',
    icon: Icons.block,
    title: 'Spam Protection',
    description: 'Block spam calls automatically and keep your phone safe from unwanted calls.',
    actionText: 'Enable Protection',
    backgroundColor: AppTheme.errorColor,
  );

  static const backupContacts = BannerConfig(
    id: 'backup_contacts',
    icon: Icons.backup,
    title: 'Backup Your Contacts',
    description: 'Keep your contacts safe by backing them up to the cloud. Never lose important contacts again.',
    actionText: 'Setup Backup',
    backgroundColor: AppTheme.warningColor,
  );
}

/// Banner configuration model
class BannerConfig {
  final String id;
  final IconData icon;
  final String title;
  final String description;
  final String actionText;
  final Color backgroundColor;

  const BannerConfig({
    required this.id,
    required this.icon,
    required this.title,
    required this.description,
    required this.actionText,
    required this.backgroundColor,
  });
}
