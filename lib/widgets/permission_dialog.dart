import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_theme.dart';
import '../providers/app_providers.dart';

/// Permission dialog for requesting app permissions
/// Shows detailed explanation and handles permission requests
class PermissionDialog extends ConsumerStatefulWidget {
  const PermissionDialog({super.key});

  @override
  ConsumerState<PermissionDialog> createState() => _PermissionDialogState();
}

class _PermissionDialogState extends ConsumerState<PermissionDialog> {
  bool _isRequesting = false;

  @override
  Widget build(BuildContext context) {
    final permissionState = ref.watch(permissionProvider);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
      ),
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacing24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            _buildHeader(),
            
            const SizedBox(height: AppTheme.spacing24),
            
            // Permissions list
            _buildPermissionsList(permissionState),
            
            const SizedBox(height: AppTheme.spacing24),
            
            // Action buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // Icon
        Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            color: AppTheme.primaryGreen.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
          ),
          child: const Icon(
            Icons.security,
            size: 32,
            color: AppTheme.primaryGreen,
          ),
        ),
        
        const SizedBox(height: AppTheme.spacing16),
        
        // Title
        const Text(
          'Permissions Required',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: AppTheme.spacing8),
        
        // Description
        const Text(
          'Easy Dialer needs these permissions to provide the best experience. Your privacy is important to us.',
          style: TextStyle(
            fontSize: 14,
            color: AppTheme.textSecondary,
            height: 1.4,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildPermissionsList(PermissionState permissionState) {
    return Column(
      children: [
        _buildPermissionItem(
          icon: Icons.phone,
          title: 'Phone Access',
          description: 'Make calls and access call history',
          isGranted: permissionState.hasPhonePermission,
          isRequired: true,
        ),
        const SizedBox(height: AppTheme.spacing12),
        _buildPermissionItem(
          icon: Icons.contacts,
          title: 'Contacts Access',
          description: 'View and manage your contacts',
          isGranted: permissionState.hasContactsPermission,
          isRequired: true,
        ),
        const SizedBox(height: AppTheme.spacing12),
        _buildPermissionItem(
          icon: Icons.storage,
          title: 'Storage Access',
          description: 'Save app data and preferences',
          isGranted: permissionState.hasCallLogPermission,
          isRequired: false,
        ),
      ],
    );
  }

  Widget _buildPermissionItem({
    required IconData icon,
    required String title,
    required String description,
    required bool isGranted,
    required bool isRequired,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: isGranted 
            ? AppTheme.successColor.withValues(alpha: 0.1)
            : AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(
          color: isGranted 
              ? AppTheme.successColor.withValues(alpha: 0.3)
              : Colors.grey.shade300,
        ),
      ),
      child: Row(
        children: [
          // Icon
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isGranted 
                  ? AppTheme.successColor
                  : AppTheme.primaryGreen,
              borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            ),
            child: Icon(
              isGranted ? Icons.check : icon,
              color: Colors.white,
              size: 20,
            ),
          ),
          
          const SizedBox(width: AppTheme.spacing16),
          
          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    if (isRequired) ...[
                      const SizedBox(width: AppTheme.spacing8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppTheme.spacing8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.errorColor,
                          borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                        ),
                        child: const Text(
                          'Required',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          
          // Status
          if (isGranted)
            const Icon(
              Icons.check_circle,
              color: AppTheme.successColor,
              size: 20,
            ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // Grant permissions button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isRequesting ? null : _requestPermissions,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryGreen,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: AppTheme.spacing16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              ),
            ),
            child: _isRequesting
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text(
                    'Grant Permissions',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
        
        const SizedBox(height: AppTheme.spacing12),
        
        // Decline button
        SizedBox(
          width: double.infinity,
          child: TextButton(
            onPressed: _isRequesting ? null : () => Navigator.of(context).pop(false),
            child: const Text(
              'Not Now',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textSecondary,
              ),
            ),
          ),
        ),
        
        const SizedBox(height: AppTheme.spacing8),
        
        // Privacy note
        Text(
          'We respect your privacy. Permissions are only used for app functionality.',
          style: TextStyle(
            fontSize: 12,
            color: AppTheme.textSecondary.withValues(alpha: 0.8),
            height: 1.3,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Future<void> _requestPermissions() async {
    setState(() {
      _isRequesting = true;
    });

    try {
      final permissionNotifier = ref.read(permissionProvider.notifier);
      final granted = await permissionNotifier.requestAllPermissions();
      
      if (mounted) {
        Navigator.of(context).pop(granted);
      }
    } catch (e) {
      if (mounted) {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to request permissions: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
        Navigator.of(context).pop(false);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRequesting = false;
        });
      }
    }
  }
}
