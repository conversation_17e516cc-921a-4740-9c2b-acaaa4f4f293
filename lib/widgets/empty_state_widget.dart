import 'package:flutter/material.dart';
import '../constants/app_theme.dart';

/// Empty state widget for Easy Dialer
/// Shows when lists are empty or no results are found
class EmptyStateWidget extends StatelessWidget {
  final IconData icon;
  final String title;
  final String description;
  final String? actionText;
  final VoidCallback? onAction;
  final Color? iconColor;

  const EmptyStateWidget({
    super.key,
    required this.icon,
    required this.title,
    required this.description,
    this.actionText,
    this.onAction,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacing32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: (iconColor ?? AppTheme.textSecondary).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
              ),
              child: Icon(
                icon,
                size: 40,
                color: iconColor ?? AppTheme.textSecondary,
              ),
            ),
            
            const SizedBox(height: AppTheme.spacing24),
            
            // Title
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: AppTheme.spacing12),
            
            // Description
            Text(
              description,
              style: const TextStyle(
                fontSize: 16,
                color: AppTheme.textSecondary,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            
            // Action button
            if (actionText != null && onAction != null) ...[
              const SizedBox(height: AppTheme.spacing24),
              ElevatedButton(
                onPressed: onAction,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryGreen,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacing24,
                    vertical: AppTheme.spacing16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                  ),
                ),
                child: Text(
                  actionText!,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Predefined empty states for Easy Dialer
class EmptyStates {
  static const noFavorites = EmptyStateConfig(
    icon: Icons.star_border,
    title: 'No Favorites Yet',
    description: 'Add contacts to favorites for quick access. Tap the star icon on any contact.',
    actionText: 'Browse Contacts',
  );

  static const noRecents = EmptyStateConfig(
    icon: Icons.history,
    title: 'No Recent Calls',
    description: 'Your call history will appear here after you make or receive calls.',
    actionText: 'Make a Call',
  );

  static const noContacts = EmptyStateConfig(
    icon: Icons.contacts,
    title: 'No Contacts',
    description: 'Add contacts to see them here. You can import from your device or add manually.',
    actionText: 'Add Contact',
  );

  static const noSearchResults = EmptyStateConfig(
    icon: Icons.search_off,
    title: 'No Results Found',
    description: 'Try a different search term or check your spelling.',
  );

  static const noBlockedNumbers = EmptyStateConfig(
    icon: Icons.block,
    title: 'No Blocked Numbers',
    description: 'Numbers you block will appear here. Block unwanted callers to avoid spam.',
  );

  static const errorState = EmptyStateConfig(
    icon: Icons.error_outline,
    title: 'Something Went Wrong',
    description: 'We encountered an error while loading your data. Please try again.',
    actionText: 'Retry',
    iconColor: AppTheme.errorColor,
  );

  static const noPermissions = EmptyStateConfig(
    icon: Icons.security,
    title: 'Permissions Required',
    description: 'Easy Dialer needs permissions to access your contacts and make calls.',
    actionText: 'Grant Permissions',
    iconColor: AppTheme.warningColor,
  );
}

/// Empty state configuration model
class EmptyStateConfig {
  final IconData icon;
  final String title;
  final String description;
  final String? actionText;
  final Color? iconColor;

  const EmptyStateConfig({
    required this.icon,
    required this.title,
    required this.description,
    this.actionText,
    this.iconColor,
  });
}
