import 'package:flutter/material.dart';
import 'package:badges/badges.dart' as badges;
import '../constants/app_theme.dart';
import '../models/call_log_model.dart';

/// Call log list item widget for Easy Dialer
/// Displays call log information with action buttons
class CallLogListItem extends StatelessWidget {
  final CallLogModel callLog;
  final VoidCallback? onTap;
  final VoidCallback? onCall;
  final VoidCallback? onMessage;
  final VoidCallback? onDelete;
  final VoidCallback? onBlock;
  final bool showActions;

  const CallLogListItem({
    super.key,
    required this.callLog,
    this.onTap,
    this.onCall,
    this.onMessage,
    this.onDelete,
    this.onBlock,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing16,
        vertical: AppTheme.spacing4,
      ),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacing16),
          child: Row(
            children: [
              // Call type icon and avatar
              _buildLeadingSection(),
              
              const SizedBox(width: AppTheme.spacing16),
              
              // Call info
              Expanded(
                child: _buildCallInfo(),
              ),
              
              // Action buttons
              if (showActions) _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLeadingSection() {
    return Stack(
      children: [
        // Avatar
        Container(
          width: AppTheme.avatarLarge,
          height: AppTheme.avatarLarge,
          decoration: BoxDecoration(
            color: callLog.photoUrl != null ? null : callLog.avatarColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            image: callLog.photoUrl != null
                ? DecorationImage(
                    image: NetworkImage(callLog.photoUrl!),
                    fit: BoxFit.cover,
                  )
                : null,
          ),
          child: callLog.photoUrl == null
              ? Center(
                  child: Text(
                    callLog.initials,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                )
              : null,
        ),
        
        // Call type indicator
        Positioned(
          bottom: 0,
          right: 0,
          child: Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: callLog.typeColor,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: Colors.white,
                width: 2,
              ),
            ),
            child: Icon(
              callLog.typeIcon,
              size: 10,
              color: Colors.white,
            ),
          ),
        ),
        
        // Spam/Blocked indicator
        if (callLog.isSpam || callLog.isBlocked)
          Positioned(
            top: 0,
            right: 0,
            child: badges.Badge(
              badgeContent: Icon(
                callLog.isBlocked ? Icons.block : Icons.warning,
                size: 8,
                color: Colors.white,
              ),
              badgeStyle: badges.BadgeStyle(
                badgeColor: callLog.isBlocked ? AppTheme.errorColor : AppTheme.warningColor,
                padding: const EdgeInsets.all(4),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildCallInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Contact name or phone number
        Row(
          children: [
            Expanded(
              child: Text(
                callLog.displayName,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            
            // Call count indicator (if multiple calls from same number)
            // TODO: Implement call count logic
          ],
        ),
        
        const SizedBox(height: 4),
        
        // Call details row
        Row(
          children: [
            // Call type and status
            Text(
              callLog.typeDisplayName,
              style: TextStyle(
                fontSize: 12,
                color: callLog.typeColor,
                fontWeight: FontWeight.w500,
              ),
            ),
            
            if (callLog.duration != null) ...[
              Text(
                ' • ${callLog.formattedDuration}',
                style: const TextStyle(
                  fontSize: 12,
                  color: AppTheme.textSecondary,
                ),
              ),
            ],
            
            const Spacer(),
            
            // Timestamp
            Text(
              callLog.relativeTime,
              style: const TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondary,
              ),
            ),
          ],
        ),
        
        // Phone number (if different from display name)
        if (callLog.contactName != null && callLog.contactName!.isNotEmpty) ...[
          const SizedBox(height: 2),
          Text(
            callLog.formattedPhoneNumber,
            style: const TextStyle(
              fontSize: 12,
              color: AppTheme.textSecondary,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
        
        // Location or additional info
        if (callLog.location != null) ...[
          const SizedBox(height: 2),
          Text(
            callLog.location!,
            style: const TextStyle(
              fontSize: 12,
              color: AppTheme.textSecondary,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Message button
        if (onMessage != null)
          IconButton(
            onPressed: onMessage,
            icon: const Icon(
              Icons.message,
              color: AppTheme.textSecondary,
              size: 20,
            ),
            tooltip: 'Send message',
            padding: const EdgeInsets.all(8),
            constraints: const BoxConstraints(
              minWidth: 36,
              minHeight: 36,
            ),
          ),
        
        // More options
        PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(value),
          itemBuilder: (context) => [
            if (onDelete != null)
              const PopupMenuItem(
                value: 'delete',
                child: ListTile(
                  leading: Icon(Icons.delete, size: 20),
                  title: Text('Delete'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            if (onBlock != null)
              PopupMenuItem(
                value: 'block',
                child: ListTile(
                  leading: Icon(
                    callLog.isBlocked ? Icons.block : Icons.block,
                    size: 20,
                  ),
                  title: Text(callLog.isBlocked ? 'Unblock' : 'Block'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            const PopupMenuItem(
              value: 'details',
              child: ListTile(
                leading: Icon(Icons.info, size: 20),
                title: Text('Details'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'add_contact',
              child: ListTile(
                leading: Icon(Icons.person_add, size: 20),
                title: Text('Add to Contacts'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
          icon: const Icon(
            Icons.more_vert,
            color: AppTheme.textSecondary,
            size: 20,
          ),
          padding: const EdgeInsets.all(8),
        ),
        
        // Call button
        if (onCall != null)
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: AppTheme.primaryGreen,
              borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            ),
            child: IconButton(
              onPressed: onCall,
              icon: const Icon(
                Icons.call,
                color: Colors.white,
                size: 18,
              ),
              tooltip: 'Call back',
              padding: EdgeInsets.zero,
            ),
          ),
      ],
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'delete':
        onDelete?.call();
        break;
      case 'block':
        onBlock?.call();
        break;
      case 'details':
        // TODO: Show call details
        break;
      case 'add_contact':
        // TODO: Add to contacts
        break;
    }
  }
}

/// Compact call log list item for smaller spaces
class CompactCallLogListItem extends StatelessWidget {
  final CallLogModel callLog;
  final VoidCallback? onTap;
  final VoidCallback? onCall;

  const CompactCallLogListItem({
    super.key,
    required this.callLog,
    this.onTap,
    this.onCall,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: onTap,
      leading: Stack(
        children: [
          Container(
            width: AppTheme.avatarMedium,
            height: AppTheme.avatarMedium,
            decoration: BoxDecoration(
              color: callLog.photoUrl != null ? null : callLog.avatarColor,
              borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              image: callLog.photoUrl != null
                  ? DecorationImage(
                      image: NetworkImage(callLog.photoUrl!),
                      fit: BoxFit.cover,
                    )
                  : null,
            ),
            child: callLog.photoUrl == null
                ? Center(
                    child: Text(
                      callLog.initials,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  )
                : null,
          ),
          Positioned(
            bottom: 0,
            right: 0,
            child: Icon(
              callLog.typeIcon,
              size: 12,
              color: callLog.typeColor,
            ),
          ),
        ],
      ),
      title: Text(
        callLog.displayName,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Text(
        '${callLog.typeDisplayName} • ${callLog.relativeTime}',
        style: const TextStyle(
          fontSize: 14,
          color: AppTheme.textSecondary,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: onCall != null
          ? IconButton(
              onPressed: onCall,
              icon: const Icon(
                Icons.call,
                color: AppTheme.primaryGreen,
                size: 20,
              ),
              tooltip: 'Call back',
            )
          : null,
    );
  }
}
