import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

class DialPad extends StatelessWidget {
  final String phoneNumber;
  final Function(String) onNumberPressed;
  final VoidCallback onDeletePressed;
  final VoidCallback onCallPressed;
  final VoidCallback? onLongDelete;

  const DialPad({
    super.key,
    required this.phoneNumber,
    required this.onNumberPressed,
    required this.onDeletePressed,
    required this.onCallPressed,
    this.onLongDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Number display
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 24.0),
            child: Text(
              phoneNumber.isEmpty ? '' : phoneNumber,
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.w400,
                letterSpacing: 2,
                color: AppConstants.primaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          // Dial pad grid
          Expanded(
            child: GridView.count(
              crossAxisCount: 3,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 0),
              mainAxisSpacing: 8,
              crossAxisSpacing: 8,
              children: [
                _buildDialButton('1', '', context),
                _buildDialButton('2', 'ABC', context),
                _buildDialButton('3', 'DEF', context),
                _buildDialButton('4', 'GHI', context),
                _buildDialButton('5', 'JKL', context),
                _buildDialButton('6', 'MNO', context),
                _buildDialButton('7', 'PQRS', context),
                _buildDialButton('8', 'TUV', context),
                _buildDialButton('9', 'WXYZ', context),
                _buildDialButton('*', '', context),
                _buildDialButton('0', '+', context),
                _buildDialButton('#', '', context),
              ],
            ),
          ),
          // Call and delete row
          Padding(
            padding: const EdgeInsets.only(bottom: 24, top: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Delete button
                IconButton(
                  icon: const Icon(Icons.backspace_outlined, color: AppConstants.secondaryTextColor, size: 28),
                  onPressed: onDeletePressed,
                  onLongPress: onLongDelete,
                  splashRadius: 28,
                ),
                const SizedBox(width: 32),
                // Call button
                FloatingActionButton(
                  backgroundColor: AppConstants.primaryColor,
                  child: const Icon(Icons.call, color: Colors.white, size: 32),
                  onPressed: onCallPressed,
                  elevation: 4,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDialButton(String digit, String letters, BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(40),
        onTap: () => onNumberPressed(digit),
        child: Container(
          alignment: Alignment.center,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                digit,
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.w400,
                  color: AppConstants.primaryTextColor,
                ),
              ),
              if (letters.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 2.0),
                  child: Text(
                    letters,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: AppConstants.secondaryTextColor,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}