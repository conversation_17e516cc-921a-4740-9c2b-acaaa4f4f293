import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'screens/main_screen.dart';
import 'screens/onboarding_screen.dart';
import 'constants/app_theme.dart';
import 'providers/app_providers.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  runApp(
    const ProviderScope(
      child: EasyDialerApp(),
    ),
  );
}

class EasyDialerApp extends ConsumerWidget {
  const EasyDialerApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final hasCompletedOnboarding = ref.watch(onboardingProvider);

    return MaterialApp(
      title: 'Easy Dialer',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.light,
      home: hasCompletedOnboarding ? const MainScreen() : const OnboardingScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
