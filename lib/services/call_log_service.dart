import 'dart:convert';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/call_log_model.dart';

/// Service for managing call logs in the Easy Dialer app
/// Provides mock data and local storage functionality
class CallLogService {
  static const String _callLogsKey = 'easy_dialer_call_logs';

  /// Get all call logs (mock data + stored call logs)
  Future<List<CallLogModel>> getCallLogs() async {
    try {
      // Get stored call logs
      final storedCallLogs = await _getStoredCallLogs();
      
      // If no stored call logs, generate mock data
      if (storedCallLogs.isEmpty) {
        final mockCallLogs = _generateMockCallLogs();
        await _saveCallLogs(mockCallLogs);
        return mockCallLogs;
      }
      
      return storedCallLogs;
    } catch (e) {
      // Return mock data if there's an error
      return _generateMockCallLogs();
    }
  }

  /// Add a new call log entry
  Future<void> addCallLog(CallLogModel callLog) async {
    final callLogs = await getCallLogs();
    callLogs.insert(0, callLog); // Add to beginning (most recent first)
    await _saveCallLogs(callLogs);
  }

  /// Delete a call log entry
  Future<void> deleteCallLog(String callLogId) async {
    final callLogs = await getCallLogs();
    callLogs.removeWhere((log) => log.id == callLogId);
    await _saveCallLogs(callLogs);
  }

  /// Clear all call logs
  Future<void> clearAllCallLogs() async {
    await _saveCallLogs([]);
  }

  /// Search call logs by contact name or phone number
  List<CallLogModel> searchCallLogs(List<CallLogModel> callLogs, String query) {
    if (query.isEmpty) return callLogs;
    
    final lowercaseQuery = query.toLowerCase();
    return callLogs.where((callLog) {
      // Search by contact name
      if (callLog.contactName?.toLowerCase().contains(lowercaseQuery) == true) {
        return true;
      }
      
      // Search by phone number
      if (callLog.phoneNumber.contains(query)) {
        return true;
      }
      
      return false;
    }).toList();
  }

  /// Get call logs by type
  Future<List<CallLogModel>> getCallLogsByType(CallType type) async {
    final callLogs = await getCallLogs();
    return callLogs.where((log) => log.type == type).toList();
  }

  /// Get missed calls
  Future<List<CallLogModel>> getMissedCalls() async {
    return await getCallLogsByType(CallType.missed);
  }

  /// Get incoming calls
  Future<List<CallLogModel>> getIncomingCalls() async {
    return await getCallLogsByType(CallType.incoming);
  }

  /// Get outgoing calls
  Future<List<CallLogModel>> getOutgoingCalls() async {
    return await getCallLogsByType(CallType.outgoing);
  }

  /// Get call logs for a specific contact
  Future<List<CallLogModel>> getCallLogsForContact(String contactId) async {
    final callLogs = await getCallLogs();
    return callLogs.where((log) => log.contactId == contactId).toList();
  }

  /// Get call logs for a specific phone number
  Future<List<CallLogModel>> getCallLogsForPhoneNumber(String phoneNumber) async {
    final callLogs = await getCallLogs();
    final cleanedNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    
    return callLogs.where((log) {
      final cleanedLogNumber = log.phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
      return cleanedLogNumber == cleanedNumber;
    }).toList();
  }

  /// Get call statistics
  Future<CallStatistics> getCallStatistics() async {
    final callLogs = await getCallLogs();
    
    int totalCalls = callLogs.length;
    int incomingCalls = callLogs.where((log) => log.type == CallType.incoming).length;
    int outgoingCalls = callLogs.where((log) => log.type == CallType.outgoing).length;
    int missedCalls = callLogs.where((log) => log.type == CallType.missed).length;
    int blockedCalls = callLogs.where((log) => log.type == CallType.blocked).length;
    
    Duration totalDuration = Duration.zero;
    for (final log in callLogs) {
      if (log.duration != null) {
        totalDuration += log.duration!;
      }
    }
    
    return CallStatistics(
      totalCalls: totalCalls,
      incomingCalls: incomingCalls,
      outgoingCalls: outgoingCalls,
      missedCalls: missedCalls,
      blockedCalls: blockedCalls,
      totalDuration: totalDuration,
    );
  }

  /// Save call logs to local storage
  Future<void> _saveCallLogs(List<CallLogModel> callLogs) async {
    final prefs = await SharedPreferences.getInstance();
    final callLogsJson = callLogs.map((log) => log.toJson()).toList();
    await prefs.setString(_callLogsKey, jsonEncode(callLogsJson));
  }

  /// Get call logs from local storage
  Future<List<CallLogModel>> _getStoredCallLogs() async {
    final prefs = await SharedPreferences.getInstance();
    final callLogsString = prefs.getString(_callLogsKey);
    
    if (callLogsString == null) return [];
    
    final callLogsJson = jsonDecode(callLogsString) as List<dynamic>;
    return callLogsJson
        .map((json) => CallLogModel.fromJson(json as Map<String, dynamic>))
        .toList();
  }

  /// Generate mock call logs for demo purposes
  List<CallLogModel> _generateMockCallLogs() {
    final random = Random();
    final phoneNumbers = [
      '+****************',
      '+****************',
      '+****************',
      '+****************',
      '+****************',
      '+****************',
      '+****************',
      '+****************',
      '+****************',
      '+****************',
    ];

    final contactNames = [
      'John Smith',
      'Sarah Johnson',
      'Michael Brown',
      'Emily Davis',
      'David Wilson',
      'Jessica Miller',
      'Christopher Lee',
      'Amanda Taylor',
      'Mom',
      'Dad',
      'Office',
      'Dr. Anderson',
      'Pizza Palace',
      'Bank Customer Service',
      'Dentist Office',
    ];

    final callLogs = <CallLogModel>[];

    // Generate calls from the last 30 days
    for (int i = 0; i < 100; i++) {
      final phoneNumber = phoneNumbers[random.nextInt(phoneNumbers.length)];
      final contactName = random.nextBool() ? contactNames[random.nextInt(contactNames.length)] : null;
      final type = CallType.values[random.nextInt(CallType.values.length - 1)]; // Exclude blocked for now
      
      // Generate realistic timestamps (last 30 days)
      final hoursAgo = random.nextInt(720); // 30 days * 24 hours
      final timestamp = DateTime.now().subtract(Duration(hours: hoursAgo));
      
      // Generate realistic call durations
      Duration? duration;
      CallStatus status;
      
      if (type == CallType.missed) {
        duration = null;
        status = CallStatus.noAnswer;
      } else if (type == CallType.rejected) {
        duration = null;
        status = CallStatus.rejected;
      } else {
        // Connected call
        final seconds = random.nextInt(1800) + 10; // 10 seconds to 30 minutes
        duration = Duration(seconds: seconds);
        status = CallStatus.connected;
      }

      final callLog = CallLogModel(
        id: 'call_log_$i',
        phoneNumber: phoneNumber,
        contactName: contactName,
        contactId: contactName != null ? 'contact_${contactName.hashCode}' : null,
        type: type,
        status: status,
        timestamp: timestamp,
        duration: duration,
        isSpam: random.nextInt(20) == 0, // 5% chance of being spam
      );

      callLogs.add(callLog);
    }

    // Sort by timestamp (newest first)
    callLogs.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    
    return callLogs;
  }
}

/// Call statistics model
class CallStatistics {
  final int totalCalls;
  final int incomingCalls;
  final int outgoingCalls;
  final int missedCalls;
  final int blockedCalls;
  final Duration totalDuration;

  const CallStatistics({
    required this.totalCalls,
    required this.incomingCalls,
    required this.outgoingCalls,
    required this.missedCalls,
    required this.blockedCalls,
    required this.totalDuration,
  });

  /// Get average call duration
  Duration get averageCallDuration {
    final connectedCalls = incomingCalls + outgoingCalls;
    if (connectedCalls == 0) return Duration.zero;
    return Duration(seconds: totalDuration.inSeconds ~/ connectedCalls);
  }

  /// Get missed call percentage
  double get missedCallPercentage {
    if (totalCalls == 0) return 0.0;
    return (missedCalls / totalCalls) * 100;
  }

  /// Get formatted total duration
  String get formattedTotalDuration {
    final totalSeconds = totalDuration.inSeconds;
    final hours = totalSeconds ~/ 3600;
    final minutes = (totalSeconds % 3600) ~/ 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }
}
