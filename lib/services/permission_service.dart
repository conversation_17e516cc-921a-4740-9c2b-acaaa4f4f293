import 'package:permission_handler/permission_handler.dart';

/// Service for handling app permissions
/// Manages phone, contacts, and call log permissions required by Easy Dialer
class PermissionService {
  /// Check if phone permission is granted
  Future<bool> hasPhonePermission() async {
    final status = await Permission.phone.status;
    return status.isGranted;
  }

  /// Check if contacts permission is granted
  Future<bool> hasContactsPermission() async {
    final status = await Permission.contacts.status;
    return status.isGranted;
  }

  /// Check if call log permission is granted
  Future<bool> hasCallLogPermission() async {
    // On newer Android versions, call log permission is included in phone permission
    final status = await Permission.phone.status;
    return status.isGranted;
  }

  /// Check if storage permission is granted
  Future<bool> hasStoragePermission() async {
    final status = await Permission.storage.status;
    return status.isGranted;
  }

  /// Request phone permission
  Future<bool> requestPhonePermission() async {
    final status = await Permission.phone.request();
    return status.isGranted;
  }

  /// Request contacts permission
  Future<bool> requestContactsPermission() async {
    final status = await Permission.contacts.request();
    return status.isGranted;
  }

  /// Request call log permission
  Future<bool> requestCallLogPermission() async {
    // On newer Android versions, call log permission is included in phone permission
    final status = await Permission.phone.request();
    return status.isGranted;
  }

  /// Request storage permission
  Future<bool> requestStoragePermission() async {
    final status = await Permission.storage.request();
    return status.isGranted;
  }

  /// Request all required permissions
  Future<bool> requestAllPermissions() async {
    final permissions = [
      Permission.phone,
      Permission.contacts,
      Permission.storage,
    ];

    final statuses = await permissions.request();
    
    // Check if all permissions are granted
    return statuses.values.every((status) => status.isGranted);
  }

  /// Check if all required permissions are granted
  Future<bool> hasAllPermissions() async {
    final phonePermission = await hasPhonePermission();
    final contactsPermission = await hasContactsPermission();
    final callLogPermission = await hasCallLogPermission();
    
    return phonePermission && contactsPermission && callLogPermission;
  }

  /// Get permission status for a specific permission
  Future<PermissionStatus> getPermissionStatus(Permission permission) async {
    return await permission.status;
  }

  /// Check if permission is permanently denied
  Future<bool> isPermissionPermanentlyDenied(Permission permission) async {
    final status = await permission.status;
    return status.isPermanentlyDenied;
  }

  /// Open app settings if permission is permanently denied
  Future<bool> openAppSettings() async {
    return await openAppSettings();
  }

  /// Get user-friendly permission name
  String getPermissionName(Permission permission) {
    switch (permission) {
      case Permission.phone:
        return 'Phone';
      case Permission.contacts:
        return 'Contacts';
      case Permission.storage:
        return 'Storage';
      default:
        return 'Unknown';
    }
  }

  /// Get permission description for user
  String getPermissionDescription(Permission permission) {
    switch (permission) {
      case Permission.phone:
        return 'Required to make phone calls and access call logs';
      case Permission.contacts:
        return 'Required to access and manage your contacts';
      case Permission.storage:
        return 'Required to store app data and settings';
      default:
        return 'Required for app functionality';
    }
  }

  /// Get permission rationale for user
  String getPermissionRationale(Permission permission) {
    switch (permission) {
      case Permission.phone:
        return 'Easy Dialer needs phone access to make calls and show your call history. This is essential for the app to function as a dialer.';
      case Permission.contacts:
        return 'Easy Dialer needs contacts access to show your contacts, add favorites, and provide caller ID functionality.';
      case Permission.storage:
        return 'Easy Dialer needs storage access to save your preferences, favorites, and app data locally on your device.';
      default:
        return 'This permission is required for the app to work properly.';
    }
  }

  /// Check if we should show permission rationale
  Future<bool> shouldShowRequestPermissionRationale(Permission permission) async {
    return await permission.shouldShowRequestRationale;
  }

  /// Request permission with rationale
  Future<PermissionRequestResult> requestPermissionWithRationale(Permission permission) async {
    // Check current status
    final currentStatus = await permission.status;
    
    if (currentStatus.isGranted) {
      return PermissionRequestResult.granted;
    }
    
    if (currentStatus.isPermanentlyDenied) {
      return PermissionRequestResult.permanentlyDenied;
    }
    
    // Request permission
    final newStatus = await permission.request();
    
    if (newStatus.isGranted) {
      return PermissionRequestResult.granted;
    } else if (newStatus.isPermanentlyDenied) {
      return PermissionRequestResult.permanentlyDenied;
    } else {
      return PermissionRequestResult.denied;
    }
  }

  /// Batch request permissions with detailed results
  Future<Map<Permission, PermissionRequestResult>> requestPermissionsWithRationale(
    List<Permission> permissions,
  ) async {
    final results = <Permission, PermissionRequestResult>{};
    
    for (final permission in permissions) {
      results[permission] = await requestPermissionWithRationale(permission);
    }
    
    return results;
  }

  /// Get all required permissions for Easy Dialer
  List<Permission> getRequiredPermissions() {
    return [
      Permission.phone,
      Permission.contacts,
      Permission.storage,
    ];
  }

  /// Get optional permissions for Easy Dialer
  List<Permission> getOptionalPermissions() {
    return [
      // Add any optional permissions here
    ];
  }

  /// Check if app has minimum required permissions
  Future<bool> hasMinimumPermissions() async {
    // At minimum, we need phone permission to function as a dialer
    return await hasPhonePermission();
  }

  /// Get missing permissions
  Future<List<Permission>> getMissingPermissions() async {
    final requiredPermissions = getRequiredPermissions();
    final missingPermissions = <Permission>[];
    
    for (final permission in requiredPermissions) {
      final status = await permission.status;
      if (!status.isGranted) {
        missingPermissions.add(permission);
      }
    }
    
    return missingPermissions;
  }

  /// Get granted permissions
  Future<List<Permission>> getGrantedPermissions() async {
    final requiredPermissions = getRequiredPermissions();
    final grantedPermissions = <Permission>[];
    
    for (final permission in requiredPermissions) {
      final status = await permission.status;
      if (status.isGranted) {
        grantedPermissions.add(permission);
      }
    }
    
    return grantedPermissions;
  }
}

/// Permission request result
enum PermissionRequestResult {
  granted,
  denied,
  permanentlyDenied,
}
