import 'dart:convert';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/contact_model.dart';

/// Service for managing contacts in the Easy Dialer app
/// Provides mock data and local storage functionality
class ContactService {
  static const String _contactsKey = 'easy_dialer_contacts';
  static const String _favoritesKey = 'easy_dialer_favorites';

  /// Get all contacts (mock data + stored contacts)
  Future<List<ContactModel>> getAllContacts() async {
    try {
      // Get stored contacts
      final storedContacts = await _getStoredContacts();
      
      // If no stored contacts, generate mock data
      if (storedContacts.isEmpty) {
        final mockContacts = _generateMockContacts();
        await _saveContacts(mockContacts);
        return mockContacts;
      }
      
      return storedContacts;
    } catch (e) {
      // Return mock data if there's an error
      return _generateMockContacts();
    }
  }

  /// Get favorite contacts
  Future<List<ContactModel>> getFavoriteContacts() async {
    final allContacts = await getAllContacts();
    return allContacts.where((contact) => contact.isFavorite).toList();
  }

  /// Add a new contact
  Future<void> addContact(ContactModel contact) async {
    final contacts = await getAllContacts();
    contacts.add(contact);
    await _saveContacts(contacts);
  }

  /// Update an existing contact
  Future<void> updateContact(ContactModel updatedContact) async {
    final contacts = await getAllContacts();
    final index = contacts.indexWhere((c) => c.id == updatedContact.id);
    if (index != -1) {
      contacts[index] = updatedContact;
      await _saveContacts(contacts);
    }
  }

  /// Delete a contact
  Future<void> deleteContact(String contactId) async {
    final contacts = await getAllContacts();
    contacts.removeWhere((c) => c.id == contactId);
    await _saveContacts(contacts);
  }

  /// Add contact to favorites
  Future<void> addToFavorites(String contactId) async {
    final contacts = await getAllContacts();
    final index = contacts.indexWhere((c) => c.id == contactId);
    if (index != -1) {
      contacts[index] = contacts[index].copyWith(isFavorite: true);
      await _saveContacts(contacts);
    }
  }

  /// Remove contact from favorites
  Future<void> removeFromFavorites(String contactId) async {
    final contacts = await getAllContacts();
    final index = contacts.indexWhere((c) => c.id == contactId);
    if (index != -1) {
      contacts[index] = contacts[index].copyWith(isFavorite: false);
      await _saveContacts(contacts);
    }
  }

  /// Search contacts by name or phone number
  List<ContactModel> searchContacts(List<ContactModel> contacts, String query) {
    if (query.isEmpty) return contacts;
    
    final lowercaseQuery = query.toLowerCase();
    return contacts.where((contact) {
      // Search by name
      if (contact.name.toLowerCase().contains(lowercaseQuery)) {
        return true;
      }
      
      // Search by display name
      if (contact.displayName?.toLowerCase().contains(lowercaseQuery) == true) {
        return true;
      }
      
      // Search by phone number
      for (final phoneNumber in contact.phoneNumbers) {
        if (phoneNumber.number.contains(query)) {
          return true;
        }
      }
      
      // Search by company
      if (contact.company?.toLowerCase().contains(lowercaseQuery) == true) {
        return true;
      }
      
      return false;
    }).toList();
  }

  /// Get contact by phone number
  Future<ContactModel?> getContactByPhoneNumber(String phoneNumber) async {
    final contacts = await getAllContacts();
    
    // Clean the phone number for comparison
    final cleanedNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    
    for (final contact in contacts) {
      for (final contactPhone in contact.phoneNumbers) {
        final cleanedContactNumber = contactPhone.number.replaceAll(RegExp(r'[^\d+]'), '');
        if (cleanedContactNumber == cleanedNumber) {
          return contact;
        }
      }
    }
    
    return null;
  }

  /// Get contact by ID
  Future<ContactModel?> getContactById(String contactId) async {
    final contacts = await getAllContacts();
    try {
      return contacts.firstWhere((c) => c.id == contactId);
    } catch (e) {
      return null;
    }
  }

  /// Block a contact
  Future<void> blockContact(String contactId) async {
    final contacts = await getAllContacts();
    final index = contacts.indexWhere((c) => c.id == contactId);
    if (index != -1) {
      contacts[index] = contacts[index].copyWith(isBlocked: true);
      await _saveContacts(contacts);
    }
  }

  /// Unblock a contact
  Future<void> unblockContact(String contactId) async {
    final contacts = await getAllContacts();
    final index = contacts.indexWhere((c) => c.id == contactId);
    if (index != -1) {
      contacts[index] = contacts[index].copyWith(isBlocked: false);
      await _saveContacts(contacts);
    }
  }

  /// Get blocked contacts
  Future<List<ContactModel>> getBlockedContacts() async {
    final contacts = await getAllContacts();
    return contacts.where((contact) => contact.isBlocked).toList();
  }

  /// Save contacts to local storage
  Future<void> _saveContacts(List<ContactModel> contacts) async {
    final prefs = await SharedPreferences.getInstance();
    final contactsJson = contacts.map((c) => c.toJson()).toList();
    await prefs.setString(_contactsKey, jsonEncode(contactsJson));
  }

  /// Get contacts from local storage
  Future<List<ContactModel>> _getStoredContacts() async {
    final prefs = await SharedPreferences.getInstance();
    final contactsString = prefs.getString(_contactsKey);
    
    if (contactsString == null) return [];
    
    final contactsJson = jsonDecode(contactsString) as List<dynamic>;
    return contactsJson
        .map((json) => ContactModel.fromJson(json as Map<String, dynamic>))
        .toList();
  }

  /// Generate mock contacts for demo purposes
  List<ContactModel> _generateMockContacts() {
    final random = Random();
    final firstNames = [
      'John', 'Jane', 'Michael', 'Sarah', 'David', 'Emily', 'Christopher', 'Amanda',
      'Matthew', 'Ashley', 'Joshua', 'Jessica', 'Andrew', 'Samantha', 'Daniel', 'Brittany',
      'James', 'Elizabeth', 'Justin', 'Taylor', 'Joseph', 'Hannah', 'Ryan', 'Alexis',
      'Brandon', 'Rachel', 'Nicholas', 'Lauren', 'Tyler', 'Kayla', 'Zachary', 'Victoria',
    ];
    
    final lastNames = [
      'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis',
      'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas',
      'Taylor', 'Moore', 'Jackson', 'Martin', 'Lee', 'Perez', 'Thompson', 'White',
      'Harris', 'Sanchez', 'Clark', 'Ramirez', 'Lewis', 'Robinson', 'Walker', 'Young',
    ];
    
    final companies = [
      'Google', 'Apple', 'Microsoft', 'Amazon', 'Meta', 'Tesla', 'Netflix', 'Adobe',
      'Salesforce', 'Oracle', 'IBM', 'Intel', 'Cisco', 'Uber', 'Airbnb', 'Spotify',
      'Twitter', 'LinkedIn', 'Zoom', 'Slack', 'Dropbox', 'Square', 'PayPal', 'eBay',
    ];
    
    final jobTitles = [
      'Software Engineer', 'Product Manager', 'Designer', 'Data Scientist', 'Marketing Manager',
      'Sales Representative', 'Customer Success Manager', 'DevOps Engineer', 'QA Engineer',
      'Business Analyst', 'Project Manager', 'HR Manager', 'Finance Manager', 'CEO', 'CTO',
    ];
    
    final contacts = <ContactModel>[];
    
    for (int i = 0; i < 50; i++) {
      final firstName = firstNames[random.nextInt(firstNames.length)];
      final lastName = lastNames[random.nextInt(lastNames.length)];
      final name = '$firstName $lastName';
      
      // Generate phone numbers
      final phoneNumbers = <PhoneNumber>[
        PhoneNumber(
          number: '+1${random.nextInt(900) + 100}${random.nextInt(900) + 100}${random.nextInt(9000) + 1000}',
          type: PhoneNumberType.mobile,
        ),
      ];
      
      // Sometimes add a work number
      if (random.nextBool()) {
        phoneNumbers.add(PhoneNumber(
          number: '+1${random.nextInt(900) + 100}${random.nextInt(900) + 100}${random.nextInt(9000) + 1000}',
          type: PhoneNumberType.work,
        ));
      }
      
      // Generate email addresses
      final emailAddresses = <EmailAddress>[
        EmailAddress(
          address: '${firstName.toLowerCase()}.${lastName.toLowerCase()}@email.com',
          type: EmailAddressType.personal,
        ),
      ];
      
      final contact = ContactModel(
        id: 'contact_$i',
        name: name,
        phoneNumbers: phoneNumbers,
        emailAddresses: emailAddresses,
        company: random.nextBool() ? companies[random.nextInt(companies.length)] : null,
        jobTitle: random.nextBool() ? jobTitles[random.nextInt(jobTitles.length)] : null,
        isFavorite: random.nextInt(10) == 0, // 10% chance of being favorite
        callCount: random.nextInt(20),
        lastContactTime: random.nextBool() 
            ? DateTime.now().subtract(Duration(days: random.nextInt(30)))
            : null,
      );
      
      contacts.add(contact);
    }
    
    // Sort contacts alphabetically
    contacts.sort((a, b) => a.name.compareTo(b.name));
    
    return contacts;
  }
}
