import 'dart:convert';
import 'dart:math';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:call_log/call_log.dart'; // Temporarily disabled
import '../models/call_log_entry.dart' as models;

class CallService {
  static final CallService _instance = CallService._internal();
  factory CallService() => _instance;
  CallService._internal();

  String? _currentCallStatus;
  String? _currentCallNumber;
  DateTime? _callStartTime;

  String? get currentCallStatus => _currentCallStatus;
  String? get currentCallNumber => _currentCallNumber;
  DateTime? get callStartTime => _callStartTime;

  Future<bool> requestPermissions() async {
    final phoneStatus = await Permission.phone.request();
    // Request call log permission for reading call history
    await Permission.phone.request();
    return phoneStatus.isGranted;
  }

  Future<void> makeCall(String phoneNumber) async {
    try {
      if (!await requestPermissions()) {
        throw Exception('Phone permission not granted');
      }

      final uri = Uri(scheme: 'tel', path: phoneNumber);
      if (await canLaunchUrl(uri)) {
        // Save call log entry BEFORE making the call
        final callLogEntry = models.CallLogEntry(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          phoneNumber: phoneNumber,
          callType: models.CallType.outgoing,
          callStatus: models.CallStatus.connected,
          timestamp: DateTime.now(),
        );

        await saveCallLog(callLogEntry);

        // Make the actual call
        await launchUrl(uri);

        _currentCallNumber = phoneNumber;
        _callStartTime = DateTime.now();

        // Call initiated successfully
      } else {
        throw Exception('Could not launch phone dialer');
      }
    } catch (e) {
      throw Exception('Failed to make call: $e');
    }
  }

  Future<void> endCall() async {
    try {
      final uri = Uri.parse('tel:');
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
        _currentCallNumber = null;
        _callStartTime = null;
      }
    } catch (e) {
      throw Exception('Failed to end call: $e');
    }
  }

  Future<List<models.CallLogEntry>> getCallLogs({int limit = 100}) async {
    try {
      if (!await requestPermissions()) {
        throw Exception('Call log permission not granted');
      }

      // Try to get real call logs from the system
      final systemLogs = await _getSystemCallLogs();
      final localLogs = await getLocalCallLogs();

      // Combine system and local logs
      final allLogs = [...systemLogs, ...localLogs];

      // Remove duplicates based on timestamp and phone number
      final uniqueLogs = <models.CallLogEntry>[];
      final seen = <String>{};

      for (final log in allLogs) {
        final key = '${log.phoneNumber}_${log.timestamp.millisecondsSinceEpoch}';
        if (!seen.contains(key)) {
          seen.add(key);
          uniqueLogs.add(log);
        }
      }

      // Sort by timestamp (newest first)
      uniqueLogs.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      return uniqueLogs.take(limit).toList();
    } catch (e) {
      // Error getting call logs, falling back to local logs
      // Fallback to local logs only
      return await getLocalCallLogs();
    }
  }

  Future<List<models.CallLogEntry>> _getSystemCallLogs() async {
    try {
      // For now, return mock data since call_log package has namespace issues
      // In a real implementation, you would use platform channels or a working call log package
      return _generateMockCallLogs();
    } catch (e) {
      // Error getting system call logs
      return [];
    }
  }

  List<models.CallLogEntry> _generateMockCallLogs() {
    final random = Random();
    final phoneNumbers = [
      '+1234567890',
      '+1987654321',
      '+1555123456',
      '+1444987654',
      '+1333456789',
    ];

    final names = [
      'John Doe',
      'Jane Smith',
      'Bob Johnson',
      'Alice Brown',
      'Charlie Wilson',
    ];

    final logs = <models.CallLogEntry>[];

    for (int i = 0; i < 20; i++) {
      final phoneNumber = phoneNumbers[random.nextInt(phoneNumbers.length)];
      final name = names[random.nextInt(names.length)];
      final callType = models.CallType.values[random.nextInt(models.CallType.values.length)];
      final timestamp = DateTime.now().subtract(Duration(hours: random.nextInt(72)));
      final duration = callType == models.CallType.missed
          ? null
          : Duration(seconds: random.nextInt(300) + 10);

      logs.add(models.CallLogEntry(
        id: timestamp.millisecondsSinceEpoch.toString(),
        phoneNumber: phoneNumber,
        contactName: name,
        callType: callType,
        callStatus: duration != null ? models.CallStatus.connected : models.CallStatus.noAnswer,
        timestamp: timestamp,
        duration: duration,
      ));
    }

    return logs;
  }





  Future<void> deleteCallLog(String callLogId) async {
    try {
      // Note: Call log deletion might not be available in newer Android versions
      // For now, we'll just log the attempt
      // Call log deletion attempted
    } catch (e) {
      throw Exception('Failed to delete call log: $e');
    }
  }

  Future<void> deleteAllCallLogs() async {
    try {
      // Note: CallLog.clearLogs() is not available in the current version
      // This would need to be implemented differently or use a different approach
      // Clear logs functionality not implemented in current version
    } catch (e) {
      throw Exception('Failed to delete all call logs: $e');
    }
  }

  Future<String?> getCallerId(String phoneNumber) async {
    try {
      // This would typically call an external API for caller ID lookup
      // For now, we'll return null as a placeholder
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<bool> isSpamNumber(String phoneNumber) async {
    try {
      // This would typically check against a spam database
      // For now, we'll return false as a placeholder
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<void> blockCall(String phoneNumber) async {
    try {
      // This would typically block the number at the system level
      // For now, we'll just log it
      // Call blocking attempted
    } catch (e) {
      throw Exception('Failed to block call: $e');
    }
  }

  Future<void> startCallListener() async {
    try {
      // Note: PhoneState.stream.listen implementation needs to be updated
      // based on the actual API of the phone_state package
      // Call listener functionality not fully implemented
    } catch (e) {
      throw Exception('Failed to start call listener: $e');
    }
  }



  Future<void> saveCallLog(models.CallLogEntry callLog) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final callLogsJson = prefs.getStringList('call_logs') ?? [];

      callLogsJson.add(jsonEncode(callLog.toJson()));

      // Keep only the last 1000 call logs
      if (callLogsJson.length > 1000) {
        callLogsJson.removeRange(0, callLogsJson.length - 1000);
      }

      await prefs.setStringList('call_logs', callLogsJson);
    } catch (e) {
      throw Exception('Failed to save call log: $e');
    }
  }

  Future<List<models.CallLogEntry>> getLocalCallLogs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final callLogsJson = prefs.getStringList('call_logs') ?? [];

      return callLogsJson.map((json) {
        return models.CallLogEntry.fromJson(jsonDecode(json));
      }).toList();
    } catch (e) {
      return [];
    }
  }

  String formatPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    final digits = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    if (digits.length == 10) {
      return '(${digits.substring(0, 3)}) ${digits.substring(3, 6)}-${digits.substring(6)}';
    } else if (digits.length == 11 && digits.startsWith('1')) {
      return '+1 (${digits.substring(1, 4)}) ${digits.substring(4, 7)}-${digits.substring(7)}';
    } else {
      return phoneNumber;
    }
  }

  bool isValidPhoneNumber(String phoneNumber) {
    final digits = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    return digits.length >= 10 && digits.length <= 15;
  }
} 