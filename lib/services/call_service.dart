import 'dart:convert';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/call_log_entry.dart';

class CallService {
  static final CallService _instance = CallService._internal();
  factory CallService() => _instance;
  CallService._internal();

  String? _currentCallStatus;
  String? _currentCallNumber;
  DateTime? _callStartTime;

  String? get currentCallStatus => _currentCallStatus;
  String? get currentCallNumber => _currentCallNumber;
  DateTime? get callStartTime => _callStartTime;

  Future<bool> requestPermissions() async {
    final phoneStatus = await Permission.phone.request();
    // Note: callLog permission is not available in newer Android versions
    return phoneStatus.isGranted;
  }

  Future<void> makeCall(String phoneNumber) async {
    try {
      if (!await requestPermissions()) {
        throw Exception('Phone permission not granted');
      }

      final uri = Uri.parse('tel:$phoneNumber');
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
        _currentCallNumber = phoneNumber;
        _callStartTime = DateTime.now();
      } else {
        throw Exception('Could not launch phone dialer');
      }
    } catch (e) {
      throw Exception('Failed to make call: $e');
    }
  }

  Future<void> endCall() async {
    try {
      final uri = Uri.parse('tel:');
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
        _currentCallNumber = null;
        _callStartTime = null;
      }
    } catch (e) {
      throw Exception('Failed to end call: $e');
    }
  }

  Future<List<CallLogEntry>> getCallLogs({int limit = 100}) async {
    try {
      if (!await requestPermissions()) {
        throw Exception('Call log permission not granted');
      }

      // TODO: Implement actual call log retrieval when call_log package is fixed
      // For now, return mock data or local stored call logs
      return await getLocalCallLogs();
    } catch (e) {
      throw Exception('Failed to get call logs: $e');
    }
  }

  CallType _mapCallType(int? callType) {
    switch (callType) {
      case 1:
        return CallType.incoming;
      case 2:
        return CallType.outgoing;
      case 3:
        return CallType.missed;
      case 4:
        return CallType.rejected;
      case 5:
        return CallType.blocked;
      default:
        return CallType.missed;
    }
  }

  CallStatus _mapCallStatus(int? duration) {
    if (duration == null || duration == 0) {
      return CallStatus.noAnswer;
    } else if (duration < 0) {
      return CallStatus.failed;
    } else {
      return CallStatus.connected;
    }
  }

  Future<void> deleteCallLog(String callLogId) async {
    try {
      // TODO: Implement actual call log deletion when call_log package is fixed
      // For now, remove from local storage
      final prefs = await SharedPreferences.getInstance();
      final callLogsJson = prefs.getStringList('call_logs') ?? [];

      callLogsJson.removeWhere((json) {
        final callLog = CallLogEntry.fromJson(jsonDecode(json));
        return callLog.id == callLogId;
      });

      await prefs.setStringList('call_logs', callLogsJson);
    } catch (e) {
      throw Exception('Failed to delete call log: $e');
    }
  }

  Future<void> deleteAllCallLogs() async {
    try {
      // Note: CallLog.clearLogs() is not available in the current version
      // This would need to be implemented differently or use a different approach
      print('Clear logs functionality not implemented in current version');
    } catch (e) {
      throw Exception('Failed to delete all call logs: $e');
    }
  }

  Future<String?> getCallerId(String phoneNumber) async {
    try {
      // This would typically call an external API for caller ID lookup
      // For now, we'll return null as a placeholder
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<bool> isSpamNumber(String phoneNumber) async {
    try {
      // This would typically check against a spam database
      // For now, we'll return false as a placeholder
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<void> blockCall(String phoneNumber) async {
    try {
      // This would typically block the number at the system level
      // For now, we'll just log it
      print('Blocking call from: $phoneNumber');
    } catch (e) {
      throw Exception('Failed to block call: $e');
    }
  }

  Future<void> startCallListener() async {
    try {
      // Note: PhoneState.stream.listen implementation needs to be updated
      // based on the actual API of the phone_state package
      print('Call listener functionality not fully implemented');
    } catch (e) {
      throw Exception('Failed to start call listener: $e');
    }
  }

  Future<void> saveCallLog(CallLogEntry callLog) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final callLogsJson = prefs.getStringList('call_logs') ?? [];
      
      callLogsJson.add(jsonEncode(callLog.toJson()));
      
      // Keep only the last 1000 call logs
      if (callLogsJson.length > 1000) {
        callLogsJson.removeRange(0, callLogsJson.length - 1000);
      }
      
      await prefs.setStringList('call_logs', callLogsJson);
    } catch (e) {
      throw Exception('Failed to save call log: $e');
    }
  }

  Future<List<CallLogEntry>> getLocalCallLogs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final callLogsJson = prefs.getStringList('call_logs') ?? [];
      
      return callLogsJson.map((json) {
        return CallLogEntry.fromJson(jsonDecode(json));
      }).toList();
    } catch (e) {
      return [];
    }
  }

  String formatPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    final digits = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    if (digits.length == 10) {
      return '(${digits.substring(0, 3)}) ${digits.substring(3, 6)}-${digits.substring(6)}';
    } else if (digits.length == 11 && digits.startsWith('1')) {
      return '+1 (${digits.substring(1, 4)}) ${digits.substring(4, 7)}-${digits.substring(7)}';
    } else {
      return phoneNumber;
    }
  }

  bool isValidPhoneNumber(String phoneNumber) {
    final digits = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    return digits.length >= 10 && digits.length <= 15;
  }
} 