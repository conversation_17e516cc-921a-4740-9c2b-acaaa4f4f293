import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/contact_service.dart';
import '../services/call_service.dart';
import '../models/contact.dart';
import '../models/call_log_entry.dart';
import '../constants/app_constants.dart';

// Theme Provider
final themeProvider = StateNotifierProvider<ThemeNotifier, ThemeMode>((ref) {
  return ThemeNotifier();
});

class ThemeNotifier extends StateNotifier<ThemeMode> {
  ThemeNotifier() : super(ThemeMode.system) {
    _loadTheme();
  }

  Future<void> _loadTheme() async {
    final prefs = await SharedPreferences.getInstance();
    final themeString = prefs.getString(AppConstants.themeKey);
    if (themeString != null) {
      switch (themeString) {
        case 'light':
          state = ThemeMode.light;
          break;
        case 'dark':
          state = ThemeMode.dark;
          break;
        default:
          state = ThemeMode.system;
      }
    }
  }

  Future<void> setTheme(ThemeMode themeMode) async {
    state = themeMode;
    final prefs = await SharedPreferences.getInstance();
    String themeString;
    switch (themeMode) {
      case ThemeMode.light:
        themeString = 'light';
        break;
      case ThemeMode.dark:
        themeString = 'dark';
        break;
      default:
        themeString = 'system';
    }
    await prefs.setString(AppConstants.themeKey, themeString);
  }
}

// Contact Provider
final contactProvider = StateNotifierProvider<ContactNotifier, AsyncValue<List<Contact>>>((ref) {
  return ContactNotifier();
});

class ContactNotifier extends StateNotifier<AsyncValue<List<Contact>>> {
  ContactNotifier() : super(const AsyncValue.loading()) {
    loadContacts();
  }

  Future<void> loadContacts() async {
    state = const AsyncValue.loading();
    try {
      await ContactService().loadContacts();
      state = AsyncValue.data(ContactService().contacts);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addToFavorites(Contact contact) async {
    try {
      await ContactService().addToFavorites(contact);
      state = AsyncValue.data(ContactService().contacts);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> removeFromFavorites(Contact contact) async {
    try {
      await ContactService().removeFromFavorites(contact);
      state = AsyncValue.data(ContactService().contacts);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> blockNumber(String phoneNumber) async {
    try {
      await ContactService().blockNumber(phoneNumber);
      state = AsyncValue.data(ContactService().contacts);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> unblockNumber(String phoneNumber) async {
    try {
      await ContactService().unblockNumber(phoneNumber);
      state = AsyncValue.data(ContactService().contacts);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  List<Contact> searchContacts(String query) {
    return ContactService().searchContacts(query);
  }

  List<Contact> get favorites => ContactService().favorites;
  List<Contact> get recentContacts => ContactService().getRecentContacts();
  List<String> get blockedNumbers => ContactService().blockedNumbers;
  bool isNumberBlocked(String phoneNumber) => ContactService().isNumberBlocked(phoneNumber);
}

// Call Log Provider
final callLogProvider = StateNotifierProvider<CallLogNotifier, AsyncValue<List<CallLogEntry>>>((ref) {
  return CallLogNotifier();
});

class CallLogNotifier extends StateNotifier<AsyncValue<List<CallLogEntry>>> {
  CallLogNotifier() : super(const AsyncValue.loading()) {
    loadCallLogs();
  }

  Future<void> loadCallLogs() async {
    state = const AsyncValue.loading();
    try {
      final callLogs = await CallService().getCallLogs();
      state = AsyncValue.data(callLogs);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteCallLog(String callLogId) async {
    try {
      await CallService().deleteCallLog(callLogId);
      await loadCallLogs();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteAllCallLogs() async {
    try {
      await CallService().deleteAllCallLogs();
      state = const AsyncValue.data([]);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> makeCall(String phoneNumber) async {
    try {
      await CallService().makeCall(phoneNumber);
      // Update contact's last call time
      final contact = ContactService().findContactByNumber(phoneNumber);
      if (contact != null && contact.id.isNotEmpty) {
        await ContactService().updateContactCallTime(contact.id, DateTime.now());
      }
    } catch (error) {
      rethrow;
    }
  }
}

// Current Call Provider
final currentCallProvider = StateNotifierProvider<CurrentCallNotifier, CurrentCallState>((ref) {
  return CurrentCallNotifier();
});

class CurrentCallState {
  final String? phoneNumber;
  final DateTime? startTime;
  final bool isInCall;

  CurrentCallState({
    this.phoneNumber,
    this.startTime,
    this.isInCall = false,
  });

  CurrentCallState copyWith({
    String? phoneNumber,
    DateTime? startTime,
    bool? isInCall,
  }) {
    return CurrentCallState(
      phoneNumber: phoneNumber ?? this.phoneNumber,
      startTime: startTime ?? this.startTime,
      isInCall: isInCall ?? this.isInCall,
    );
  }
}

class CurrentCallNotifier extends StateNotifier<CurrentCallState> {
  CurrentCallNotifier() : super(CurrentCallState()) {
    _initializeCallListener();
  }

  Future<void> _initializeCallListener() async {
    try {
      await CallService().startCallListener();
    } catch (e) {
      // Handle error
    }
  }

  void startCall(String phoneNumber) {
    state = state.copyWith(
      phoneNumber: phoneNumber,
      startTime: DateTime.now(),
      isInCall: true,
    );
  }

  void endCall() {
    state = state.copyWith(
      phoneNumber: null,
      startTime: null,
      isInCall: false,
    );
  }

  Duration? get callDuration {
    if (state.startTime == null) return null;
    return DateTime.now().difference(state.startTime!);
  }
}

// Search Provider
final searchProvider = StateNotifierProvider<SearchNotifier, String>((ref) {
  return SearchNotifier();
});

class SearchNotifier extends StateNotifier<String> {
  SearchNotifier() : super('');

  void setSearchQuery(String query) {
    state = query;
  }

  void clearSearch() {
    state = '';
  }
} 